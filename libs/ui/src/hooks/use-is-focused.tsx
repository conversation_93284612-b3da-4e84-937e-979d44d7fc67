import type { RefObject } from 'react'
import { useEffect, useRef, useState } from 'react'

export function useIsFocused<T extends HTMLElement>(): [
  RefObject<T | null>,
  boolean
] {
  const [isFocused, setIsFocused] = useState(false)
  const ref = useRef<T | null>(null)

  useEffect(() => {
    const handleFocus = () => setIsFocused(true)
    const handleBlur = () => setIsFocused(false)

    const element = ref.current

    if (element) {
      element.addEventListener('focus', handleFocus)
      element.addEventListener('blur', handleBlur)
    }

    return () => {
      if (element) {
        element.removeEventListener('focus', handleFocus)
        element.removeEventListener('blur', handleBlur)
      }
    }
  }, [])

  return [ref, isFocused]
}
