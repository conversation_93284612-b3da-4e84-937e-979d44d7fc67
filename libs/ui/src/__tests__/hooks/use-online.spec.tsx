import '@testing-library/jest-dom/vitest'
import { describe, it, expect, beforeEach } from 'vitest'
import { fireEvent, renderHook } from '@testing-library/react'
import { act } from 'react'

import { useOnLine } from '../../hooks/use-online'

const mockSonner = {
  toast: {
    error: () => {
      return
    },
    success: () => {
      return
    },
  },
}

describe('useOnLine hook', () => {
  beforeEach(() => {
    mockSonner.toast.error = () => {
      return
    }
    mockSonner.toast.success = () => {
      return
    }
  })

  it('initial status is true if online', () => {
    Object.defineProperty(window, 'navigator', {
      value: { onLine: true },
      configurable: true,
    })
  })

  it('updates status when going offline', () => {
    const { result } = renderHook(() => useOnLine())

    act(() => {
      fireEvent(window, new Event('offline'))
    })

    expect(result.current).toBe(false)
  })

  it('updates status when going online', () => {
    const { result } = renderHook(() => useOnLine())

    act(() => {
      fireEvent(window, new Event('offline'))
    })
    act(() => {
      fireEvent(window, new Event('online'))
    })

    expect(result.current).toBe(true)
  })
})
