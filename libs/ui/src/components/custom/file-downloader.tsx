import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, TooltipTrigger } from '../base'
import { toast } from 'sonner'
import { FileSpreadsheetIcon, Loader2 } from 'lucide-react'
import { apiConfig } from '@libs/common'
import { forwardRef, useState } from 'react'
import { t } from 'i18next'

const { basePath } = apiConfig

interface FileDownloaderProps {
  pathname: string
  fileType?: 'csv' | 'pdf' | 'zip'
  fileName?: string
  className?: string
}

export const FileDownloader = forwardRef<
  HTMLButtonElement,
  FileDownloaderProps
>(({ className, pathname, fileType = 'csv', fileName = 'export' }, ref) => {
  const [isLoading, setIsLoading] = useState(false)

  const handleDownload = async () => {
    setIsLoading(true)

    toast.info('Downloading file...The operation may take some time')

    const baseUrl = `${basePath}${pathname}`

    try {
      const response = await fetch(baseUrl, {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const blob = await response.blob()

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      const contentDisposition = response.headers.get('content-disposition')
      const filename = fileName
        ? fileName
        : contentDisposition
        ? contentDisposition.split('filename=')[1].replace(/"/g, '')
        : 'export.csv'

      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Download failed:', error)
      toast.error('Failed to download file. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          ref={ref}
          onClick={handleDownload}
          variant="ghost"
          disabled={isLoading}
          dataTestId="download-file-button"
          className={className}
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <FileSpreadsheetIcon />
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <div>
          {fileType ? 'Export to ' + fileType.toUpperCase() : t('exportToCsv')}
        </div>
      </TooltipContent>
    </Tooltip>
  )
})
