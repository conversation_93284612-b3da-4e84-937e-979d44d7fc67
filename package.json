{"name": "@frontend-monorepo/source", "version": "0.0.0", "license": "MIT", "scripts": {"dashboard:dev": "pnpm exec nx run dashboard:serve", "dashboard:type-check": "pnpm exec tsc -b apps/dashboard/tsconfig.json --incremental", "test:all": "pnpm exec nx run-many --target=test --all --coverage --watch=false", "test:affected": "pnpm exec nx affected:test", "lint:all": "pnpm exec nx run-many --target=lint --all", "lint:affected": "pnpm exec nx affected -t lint", "build:all": "pnpm exec nx run-many --target=build --all", "build:affected": "pnpm exec nx affected -t build", "front-service:update": "pnpm up prime-front-service-client --latest", "codeartifact:login": "aws codeartifact login --tool npm --repository main-repo --domain primesec > /dev/null 2>&1", "generate-mocks": "node scripts/generate-mocks.js", "format": "prettier --write .", "tsc:build": "npx tsc -b apps/dashboard/tsconfig.json --incremental", "prepare": "husky"}, "private": true, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-react": "^7.27.1", "@nx/devkit": "21.2.3", "@nx/eslint": "21.2.3", "@nx/eslint-plugin": "21.2.3", "@nx/jest": "21.2.3", "@nx/js": "21.2.3", "@nx/next": "21.2.3", "@nx/node": "21.2.3", "@nx/playwright": "21.2.3", "@nx/react": "21.2.3", "@nx/vite": "21.2.3", "@nx/web": "21.2.3", "@nx/workspace": "21.2.3", "@testing-library/jest-dom": "^6.4.2", "@types/jest": "29.5.14", "@types/node": "18.16.9", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "7.17.0", "@typescript-eslint/parser": "7.17.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "autoprefixer": "10.4.21", "dotenv": "^16.4.7", "eslint": "~8.57.1", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.32.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-playwright": "^0.15.3", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^9.1.7", "jest": "29.7.0", "jest-environment-node": "^29.7.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "lint-staged": "^15.5.2", "msw": "^2.7.6", "nx": "21.2.3", "postcss": "8.5.6", "prettier": "^2.8.8", "tailwindcss": "3.4.15", "ts-node": "10.9.2", "tslib": "^2.8.1", "typescript": "5.8.3", "vite": "6.3.5", "vite-plugin-dts": "4.5.4", "vitest": "^1.6.1"}, "dependencies": {"@datadog/browser-rum": "^6.12.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@nivo/line": "^0.87.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^5.81.2", "@tanstack/react-table": "^8.21.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "^14.6.1", "@types/qrcode": "^1.5.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "i18next": "^23.16.8", "immer": "^10.1.1", "input-otp": "^1.4.2", "jotai": "^2.12.5", "jotai-scope": "^0.7.1", "launchdarkly-react-client-sdk": "^3.6.1", "lucide-react": "^0.446.0", "mermaid": "^11.7.0", "next-themes": "^0.3.0", "nuqs": "^2.4.3", "prime-front-service-client": "^1.0.16165015882", "qrcode": "^1.5.4", "query-string": "^9.2.1", "react": "18.3.1", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-hook-form": "^7.58.1", "react-i18next": "^14.1.3", "react-intersection-observer": "^9.15.1", "react-markdown": "^10.0.1", "react-pdf": "^9.2.1", "react-router": "^7.5.3", "react-syntax-highlighter": "^15.6.1", "sonner": "^2.0.5", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.67"}, "msw": {"workerDirectory": ["apps/dashboard/public"]}, "pnpm": {"overrides": {"@babel/runtime": ">=7.26.10", "@babel/helpers": ">=7.26.10", "cross-spawn@<7.0.5": ">=7.0.5", "path-to-regexp@<0.1.12": ">=0.1.12", "nanoid@<3.3.8": ">=3.3.8", "prismjs@<1.30.0": ">=1.30.0", "axios": ">=1.8.2", "esbuild": " >=0.25.0", "tar-fs@<2.1.3": ">=2.1.3", "koa": ">=2.16.1", "brace-expansion@1.1.11": "1.1.12", "brace-expansion@2.0.1": "2.0.2"}}}