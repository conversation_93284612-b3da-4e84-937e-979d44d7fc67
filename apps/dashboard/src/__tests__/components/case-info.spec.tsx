import React from 'react'
import '@testing-library/jest-dom/vitest'
import { describe, expect, it } from 'vitest'
import { render } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import type {
  ExternalCase,
  ExternalIssueAnalysis,
} from 'prime-front-service-client'
import { CaseStatus } from 'prime-front-service-client'
import { vi } from 'vitest'
import { CaseInfo } from '../../pages/workroom-details/components/case-info'
import { Providers } from '../../components/providers'

const mockCaseInfo: ExternalCase = {
  title: 'Sample Issue',
  status: CaseStatus.open,
  comments: [],
  labels: ['two', 'three', 'seven', 'six'],
  provider_fields: {
    issue_name: 'Sample Issue',
    created_at: new Date().toLocaleDateString(),
    issue_id: 'ISSUE-123',
    creator: '<PERSON>',
    assignee: '<PERSON>',
    status: 'Open',
    issue_link: 'http://example.com',
    creator_email: null,
    assignee_email: null,
  },
  issue_analysis: {
    issue_links: [],
    short_ai_summary: '<p>Sample Ticket Summary</p>',
    risk_factors: {
      confidentiality: 5,
      confidentiality_explanation: null,
      integrity: 5,
      integrity_explanation: null,
      availability: 10,
      availability_explanation: null,
      third_party_management: null,
      third_party_management_explanation: null,
      compliance: null,
      compliance_explanation: null,
      severity: null,
      severity_explanation: null,
      scope: null,
      scope_explanation: null,
      assessment_result: {
        attack_complexity_likelihood_score: 'high',
        attack_complexity_explanation:
          'The proposed single sign-on (SSO) service could be a target for adversaries to gain initial access to the system, as it serves as a centralized authentication mechanism. Attacks against SSO services, such as credential theft, are relatively common and can be executed using publicly available tools and techniques. The complexity of these attacks is generally considered to be on the lower end of the spectrum.',
        security_controls_presence: 'unknown',
        security_controls_presence_explanation:
          'The ticket does not provide any information about the presence or absence of compensating security controls for the proposed SSO service.',
        exposure_presence: 'unknown',
        exposure_presence_explanation:
          'The ticket does not specify whether the SSO service will be publicly exposed or if it will be internal-only.',
        third_party_presence: 'unknown',
        third_party_presence_explanation:
          'The ticket does not mention any integration with third-party services or components.',
        likelihood_explanation:
          'Based on the high likelihood of attack complexity and the lack of information about security controls, exposure, and third-party integrations, the overall likelihood of the security risks materializing is considered to be medium.',
        short_summary:
          'The proposed single sign-on service poses a security risk as it could serve as a centralized access point for adversaries to gain initial access and further compromise the system.',
        long_summary:
          'The proposed single sign-on (SSO) service could serve as a centralized access point for an adversary to gain initial access to the system, potentially leading to further compromise. The SSO service could be targeted for credential theft, allowing unauthorized access, and could be used as a command and control mechanism or to gather information about the environment. Additionally, the SSO service may not comply with security and data management best practices, leading to issues such as unlawful personal data processing, inadequate cybersecurity risk management, and a loss of plausible deniability for users. Overall, the centralized nature of the SSO service makes it a high-value target for adversaries, posing significant security risks to the organization.',
        COMPLEXITY_ADJUSTMENTS: null,
        attack_complexity_likelihood_contribution: 0.5,
        attack_complexity_likelihood_weight: 25,
        security_controls_contribution: 0,
        security_controls_weight: 25,
        exposure_contribution: 0,
        exposure_weight: 25,
        third_party_contribution: 0,
        third_party_weight: 25,
        likelihood_score: 56,
        attack_complexity: 12,
        security_controls: 0,
        public_exposure: 0,
        third_party: 0,
      },
      resources: {
        cloud_resources: [],
        saas_resources: [],
        general_resources: [
          {
            name: 'Single sign-on',
            type: 'Identity Federation',
            category: 'general',
            impact_score: 85,
          },
        ],
        impact_score: 85,
      },
      confidentiality_level: 'medium',
      integrity_level: 'medium',
      availability_level: 'high',
      third_party_management_level: null,
      compliance_level: null,
    },
  } as unknown as ExternalIssueAnalysis,
} as unknown as ExternalCase

describe('CaseInfo', () => {
  it('renders issue name', () => {
    const { getByText } = render(
      <MemoryRouter>
        <CaseInfo
          successComment={false}
          caseInfo={mockCaseInfo}
          pendingComment={false}
          setIssueComment={vi.fn()}
          refetchCase={vi.fn()}
          reopenCase={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('Sample Issue')).toBeInTheDocument()
  })

  it('renders ticket summary when present', () => {
    const { getByText } = render(
      <MemoryRouter>
        <CaseInfo
          successComment={false}
          caseInfo={mockCaseInfo}
          pendingComment={false}
          setIssueComment={vi.fn()}
          refetchCase={vi.fn()}
          reopenCase={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('Expected Outcome')).toBeInTheDocument()
    expect(getByText('Sample Ticket Summary')).toBeInTheDocument()
  })

  it.skip('renders JiraIssueDetails component', () => {
    const { getByText } = render(
      <MemoryRouter>
        <CaseInfo
          successComment={false}
          caseInfo={mockCaseInfo}
          pendingComment={false}
          setIssueComment={vi.fn()}
          refetchCase={vi.fn()}
          reopenCase={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByText('ISSUE-123')).toBeInTheDocument()
    expect(getByText('John Doe')).toBeInTheDocument()
    expect(getByText('Jane Doe')).toBeInTheDocument()
    expect(getByText('Open')).toBeInTheDocument()
  })

  it('renders comment input and button when status is not Done', () => {
    const { getByPlaceholderText, getByText } = render(
      <MemoryRouter>
        <CaseInfo
          successComment={false}
          caseInfo={mockCaseInfo}
          pendingComment={false}
          setIssueComment={vi.fn()}
          refetchCase={vi.fn()}
          reopenCase={vi.fn()}
        />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )
    expect(getByPlaceholderText('Type..')).toBeInTheDocument()
    expect(getByText('Activity Log')).toBeInTheDocument()
  })
})
