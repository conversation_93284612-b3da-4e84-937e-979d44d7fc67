/*eslint-disable max-lines*/
import {
  Button,
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormControl,
  FormDescription,
  Input,
  ClipLoader,
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Separator,
} from '@libs/ui'
import { Loader2 } from 'lucide-react'
import {
  type FormFieldsType,
  baseFormFields,
  editFormSchema,
  systemFormSchema,
} from '../utils'

import { SourceSettings } from './source-settings'
import { t } from 'i18next'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import type { z } from 'zod'
import type {
  JiraInsertModel,
  ProjectResponse,
} from 'prime-front-service-client'
import {
  useDeleteSource,
  useGetJiraProjectsBySourceId,
  useGetSource,
  useUpdateJiraSource,
} from '../../../../api/use-sources-api'
import { useSetAtom } from 'jotai'
import { sourceModalAtom, refetchSourcesAtom } from '../page'
import { isOlderThanNDays } from './utils'

interface UpdateSourceFormProps {
  sourceId: number
}

export const UpdateSourceForm = ({ sourceId }: UpdateSourceFormProps) => {
  const setOpen = useSetAtom(sourceModalAtom)

  const { data: source, isLoading } = useGetSource(+sourceId)
  const updateSourceMutation = useUpdateJiraSource()
  const deleteSourceMutation = useDeleteSource()
  const getJiraProjectsBySourceIdMutation = useGetJiraProjectsBySourceId()
  const [hasMoreProjects, setHasMoreProjects] = useState(true)
  const [isFetchingMore, setIsFetchingMore] = useState(false)
  const [projectOffset, setProjectOffset] = useState(0)

  const refetchSources = useSetAtom(refetchSourcesAtom)

  const [step, setStep] = useState<'initial' | 'settings'>('initial')
  const [projectsOptions, setProjectsOptions] = useState<
    Array<ProjectResponse>
  >([])
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])

  const baseForm = useForm<z.infer<typeof editFormSchema>>({
    resolver: zodResolver(editFormSchema),
    defaultValues: {
      jira_url: '',
      email: '',
      api_token: '',
      name: '',
    },
  })

  const sourceSettingsForm = useForm<{
    projects: string[]
    since_in_days: number
  }>({
    resolver: zodResolver(systemFormSchema),
    defaultValues: {
      projects: [],
      since_in_days: 30,
    },
  })

  const projects = sourceSettingsForm.getValues('projects')

  const handleProjectChange = (project: string) => {
    const newProjects = projects.includes(project)
      ? projects.filter((p) => p !== project)
      : [...projects, project]
    sourceSettingsForm.setValue('projects', newProjects)
    setSelectedProjects(newProjects)
  }

  const onContinue = async () => {
    const commonFormValues = baseForm.getValues()
    const settingsFormValues = sourceSettingsForm.getValues()

    const jiraInsertModel: JiraInsertModel = {
      email: commonFormValues.email,
      jira_url: commonFormValues.jira_url,
      api_token: commonFormValues.api_token || '',
      name: commonFormValues.name,
    }

    if (settingsFormValues.projects.length > 0) {
      jiraInsertModel.jql_parameters_filter = {
        projects: selectedProjects,
        since_in_days: settingsFormValues.since_in_days || 90,
      }
    }

    try {
      await updateSourceMutation.mutateAsync({
        source_id: sourceId,
        JiraInsertModel: {
          email: jiraInsertModel.email,
          api_token: jiraInsertModel.api_token,
          jira_url: jiraInsertModel.jira_url,
          jql_parameters_filter: jiraInsertModel.jql_parameters_filter || null,
          name: jiraInsertModel.name,
        },
      })
      const offset = 0
      const limit = 100

      const firstPage = await getJiraProjectsBySourceIdMutation.mutateAsync({
        source_id: sourceId,
        limit: limit,
        offset: offset,
      })

      const shouldStop = firstPage.results.some((project) =>
        isOlderThanNDays(project?.insight?.last_issue_update_time, 90)
      )

      setProjectsOptions(firstPage.results)
      setProjectOffset(limit)
      setHasMoreProjects(!shouldStop && (firstPage.has_next ?? false))
      setStep('settings')
    } catch (error: any) {
      const detail = await error?.response?.json()?.detail
      toast.error(detail || t('errors.failedToFetchProjects'))
    }
  }

  const loadMoreProjects = async () => {
    if (!hasMoreProjects || isFetchingMore) return
    setIsFetchingMore(true)

    const nextPage = await getJiraProjectsBySourceIdMutation.mutateAsync({
      source_id: sourceId,
      limit: 100,
      offset: projectOffset,
    })

    const shouldStop = nextPage.results.some((project) =>
      isOlderThanNDays(project?.insight?.last_issue_update_time, 90)
    )

    setProjectsOptions((prev) => [...prev, ...nextPage.results])
    setProjectOffset((prev) => prev + 100)
    setHasMoreProjects(!shouldStop && (nextPage.has_next ?? false))
    setIsFetchingMore(false)
  }

  const handleFormSubmit = async () => {
    const settingsValues = sourceSettingsForm.getValues()

    await updateSourceMutation.mutateAsync(
      {
        source_id: Number(sourceId),
        JiraInsertModel: {
          email: baseForm.getValues().email,
          jira_url: baseForm.getValues().jira_url,
          api_token: baseForm.getValues().api_token || '',
          name: baseForm.getValues().name,
          jql_parameters_filter: {
            projects: selectedProjects.filter((p) =>
              projectsOptions.some((option) => option.key === p)
            ),
            since_in_days: settingsValues?.since_in_days,
          },
        } as JiraInsertModel,
      },
      {
        onSuccess: async () => {
          toast.success(t('sourceUpdatedSuccessfully'))

          baseForm.reset()
          sourceSettingsForm.reset()
          setOpen(false)
          refetchSources((prev) => prev + 1)
        },
        onError: async () => {
          toast.error(t('errors.failedToUpdateSource'))
        },
      }
    )
  }

  const onDelete = (source_id: number) => {
    deleteSourceMutation.mutate(source_id, {
      onSuccess: async () => {
        toast.success(t('sourceDeletedSuccessfully'))
        setOpen(false)
        refetchSources((prev) => prev + 1)
      },
      onError: () => {
        toast.error(t('errors.failedToDeleteSource'))
      },
    })
  }

  useEffect(() => {
    const sourceInfo = source?.info as JiraInsertModel
    if (sourceInfo) {
      baseForm.reset({
        name: sourceInfo.name || '',
        jira_url: sourceInfo.jira_url || '',
        email: sourceInfo.email || '',
        api_token: sourceInfo.api_token || '',
      })
      setSelectedProjects(sourceInfo.jql_parameters_filter?.projects || [])
      if (sourceInfo.jql_parameters_filter) {
        sourceSettingsForm.reset({
          projects: sourceInfo.jql_parameters_filter.projects,
          since_in_days: sourceInfo.jql_parameters_filter.since_in_days,
        })
      }
    }
  }, [baseForm, sourceSettingsForm, source])

  const isFormValid =
    step === 'initial'
      ? baseForm.formState.isValid
      : sourceSettingsForm.formState.isValid

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <ClipLoader />
      </div>
    )
  }

  if (step === 'settings') {
    return (
      <SourceSettings
        baseForm={baseForm}
        sourceSettingsForm={sourceSettingsForm}
        projectsOptions={projectsOptions}
        updatePending={updateSourceMutation.isPending}
        selectedProjects={selectedProjects}
        loadMoreProjects={loadMoreProjects}
        hasMoreProjects={hasMoreProjects}
        isFetchingMore={isFetchingMore}
        handleProjectChange={handleProjectChange}
        onSubmit={handleFormSubmit}
      />
    )
  }

  if (step === 'initial') {
    return (
      <div className="form-wrapper">
        <Form {...baseForm}>
          <form
            onSubmit={baseForm.handleSubmit(onContinue)}
            className="space-y-4"
          >
            {step === 'initial' && (
              <>
                {baseFormFields.map(({ fieldName, description }) => (
                  <FormField
                    key={fieldName}
                    name={fieldName as FormFieldsType}
                    control={baseForm.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t(`sourceForm.${fieldName}`)}
                          <span className="text-red-500 mx-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder={
                              fieldName === 'api_token' ? '**********' : ''
                            }
                          />
                        </FormControl>
                        {description && (
                          <FormDescription className="">
                            {description}
                          </FormDescription>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
                <Button
                  type="submit"
                  disabled={
                    !isFormValid ||
                    updateSourceMutation?.isPending ||
                    getJiraProjectsBySourceIdMutation?.isPending
                  }
                  dataTestId="continue-button"
                >
                  {(updateSourceMutation?.isPending ||
                    getJiraProjectsBySourceIdMutation?.isPending) && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Continue
                </Button>
              </>
            )}
          </form>
        </Form>
        <div className="mt-12">
          <Separator className="my-4" />
          <div className="flex items-center justify-between">
            <p className="text-xs">
              {t('sourceDeleteConfirmWarning')}.
              <br />
              {t('youCannotUndoThisAction')}.
            </p>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  size="sm"
                  className="capitalize"
                  dataTestId="delete-source"
                  disabled={deleteSourceMutation.isPending}
                >
                  {deleteSourceMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  <span>
                    {t('delete')} {t('source')}
                  </span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {t('areYouAbsolutelySure')}?
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('thisActionCannotBeUndone')}.{' '}
                    {t('thisWillPermanentlyDeleteSource')}.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
                  <AlertDialogAction asChild>
                    <Button
                      className="capitalize bg-red-500  hover:bg-red-500/90"
                      dataTestId="delete-source-confirm-button"
                      onClick={() => onDelete(Number(sourceId))}
                    >
                      {t('delete')} {t('source')}
                    </Button>
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    )
  }
}
