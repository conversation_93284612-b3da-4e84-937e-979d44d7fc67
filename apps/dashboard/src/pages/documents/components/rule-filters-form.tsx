import type { ProviderFieldInfoOptions } from 'prime-front-service-client'
import type { FilterItem } from '@libs/ui'
import { RuleFilter } from './rule-filter'
import { Button } from '@libs/ui'
import { Trash2 } from 'lucide-react'
import { useEffect } from 'react'
import { t } from 'i18next'

interface RulesFiltersFormProps {
  filters: FilterItem[]
  fields: ProviderFieldInfoOptions[]
  onFiltersChange: (filters: FilterItem[]) => void
}

export const RulesFiltersForm = ({
  filters,
  fields,
  onFiltersChange,
}: RulesFiltersFormProps) => {
  const isFilterValid = (filter: FilterItem) => {
    return (
      filter.field !== '' &&
      filter.op !== '' &&
      filter.value !== '' &&
      filter.value !== null &&
      filter.value !== undefined &&
      (Array.isArray(filter.value) ? filter.value.length > 0 : true)
    )
  }

  const ensureSingleEmptyAtEnd = (filters: FilterItem[]) => {
    const hasEmpty = filters.some((f) => !isFilterValid(f))
    if (!hasEmpty) {
      return [...filters, { field: '', op: 'eq', value: '' }]
    }
    return filters
  }

  const visibleFilters = filters.filter(
    (f) => f.field !== 'provider_fields.project'
  )
  const handleRemoveFilter = (index: number) => {
    const updatedFilters = filters.filter((_, i) => i !== index)
    const allRemainingValid = updatedFilters.every(isFilterValid)
    if (updatedFilters.length === 0) {
      onFiltersChange([{ field: '', op: 'eq', value: '' }])
    } else if (allRemainingValid) {
      onFiltersChange([...updatedFilters, { field: '', op: 'eq', value: '' }])
    } else {
      onFiltersChange(updatedFilters)
    }
  }

  const onFilterChange = (updatedFilter: FilterItem, index: number) => {
    const updatedFilters = [...filters]
    updatedFilters[index] = updatedFilter
    onFiltersChange(ensureSingleEmptyAtEnd(updatedFilters))
  }

  useEffect(() => {
    const hasEmpty = visibleFilters.some((f) => !isFilterValid(f))
    const allValid =
      visibleFilters.length > 0 && visibleFilters.every(isFilterValid)
    if (allValid && !hasEmpty) {
      onFiltersChange([...filters, { field: '', op: 'eq', value: '' }])
    }
  }, [visibleFilters])

  return (
    <div className="flex flex-col gap-4 p-4 rounded-md border-slate-300 bg-slate-100">
      {visibleFilters?.map((filter, index) => (
        <div
          key={`${filter.field}-${index}`}
          className="flex flex-col items-center"
        >
          <div className="rounded-md bg-white p-4 flex items-center gap-2 w-full">
            <RuleFilter
              selectedFilter={filter}
              fields={fields}
              onFilterChange={(updatedFilter) =>
                onFilterChange(updatedFilter, index)
              }
            />
            <Button
              variant="ghost"
              onClick={() => handleRemoveFilter(index)}
              dataTestId="remove-filter"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>

          {index < visibleFilters.length - 1 && (
            <div className="text-xs font-medium uppercase mt-4">{t('and')}</div>
          )}
        </div>
      ))}
    </div>
  )
}
