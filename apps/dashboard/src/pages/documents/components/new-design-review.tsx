/*eslint-disable max-lines*/
import {
  Button,
  Checkbox,
  ConfluenceIcn,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  GoogleDocIcon,
  Input,
  PdfIcon,
} from '@libs/ui'
import type React from 'react'
import { useState, useEffect } from 'react'
import { t } from 'i18next'
import { toast } from 'sonner'
import {
  errorsMap,
  useStartSecurityReviewForUrls,
  useUploadDesignDocs,
} from '../../../api/use-design-docs-api'
import { Link } from 'react-router'
import { DocumentUpload } from './document-upload'
import type { ResponseError } from 'prime-front-service-client'
import { useFlagsWrapper } from '../../../hooks/use-flags-wrapper'
import { Loader2, Plus, X } from 'lucide-react'
import { duplicateCode } from '../../../api/error-map'

interface UrlField {
  id: string
  value: string
}

const MAX_SIZE = 4.5

interface NewDesignReviewProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  refetchDocs?: () => void
}

export const NewDesignReview = ({
  open,
  onOpenChange,
  refetchDocs,
}: NewDesignReviewProps) => {
  const { confluenceLinkDesignReview, googleLinkDesignReview } =
    useFlagsWrapper()
  const [mode, setMode] = useState<
    'file' | 'link' | 'gDoc' | 'jiraProject' | null
  >(null)
  const [urlFields, setUrlFields] = useState<UrlField[]>([
    { id: crypto.randomUUID(), value: '' },
  ])
  const [processAsOne, setProcessAsOne] = useState(false)
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set())

  const [title, setTitle] = useState<
    'addSecurityReview' | 'uploadFile' | 'uploadLink' | 'addJiraProject'
  >('addSecurityReview')

  const { mutate: uploadDesignDocsMutate, isPending: uploadPending } =
    useUploadDesignDocs()

  useEffect(() => {
    if (mode) {
      setTitle(
        mode === 'link'
          ? 'uploadLink'
          : mode === 'jiraProject'
          ? 'addJiraProject'
          : 'uploadFile'
      )
    } else {
      setTitle('addSecurityReview')
    }
  }, [mode])

  const handleUpload = (files: File[], processAsOne?: boolean) => {
    const loadingToastId = toast.loading(
      `Uploading ${files.length} document(s)...`
    )

    uploadDesignDocsMutate(
      {
        files,
        process_as_one: processAsOne ?? false,
      },
      {
        onSuccess: () => {
          toast.success(
            t('documentsUploadSuccess', { docsNumber: files.length }),
            {
              description: (
                <div>
                  <Link
                    to={{
                      pathname: '/settings/tasks-status',
                    }}
                    className="underline"
                  >
                    {t('checkStatusOfUploadedFiles')}
                  </Link>
                </div>
              ),
              duration: 8000,
            }
          )

          onOpenChange(false)
          if (refetchDocs) {
            refetchDocs()
          }
        },
        onError: async (error: unknown) => {
          const errorStatus = await (error as ResponseError)?.response?.status

          if (errorStatus === duplicateCode) {
            toast.error(t('errors.designReviewAlreadyExist'))
          } else {
            toast.error(t('errors.failedToUploadDocuments'))
          }
        },
        onSettled: () => {
          toast.dismiss(loadingToastId)
        },
      }
    )
  }

  useEffect(() => {
    if (open) {
      setMode(null)
      resetUrls()
    }
  }, [open])

  const { mutate: startSecurityReviewForUrls, isPending: isUrlPending } =
    useStartSecurityReviewForUrls()

  const handleLinkUpload = () => {
    const validUrls = urlFields
      .map((field) => field.value)
      .filter((url) => url.trim() !== '' && validateUrl(url))

    if (validUrls.length === 0) {
      toast.error(t('errors.noValidUrls'))
      return
    }

    startSecurityReviewForUrls(
      {
        urls: validUrls,
        process_as_one: processAsOne,
      },
      {
        onSuccess: async () => {
          onLinkSuccess()
        },
        onError: async (error: Error) => {
          await onLinkError(error)
        },
      }
    )
  }

  const onLinkSuccess = () => {
    toast.success(t('uploadLinkSuccessfully'), {
      description: (
        <div>
          <Link
            to={{
              pathname: '/settings/tasks-status',
            }}
            className="underline"
          >
            {t('checkStatusOfUpload')}
          </Link>
        </div>
      ),
      duration: 8000,
    })
    onOpenChange(false)
    if (refetchDocs) {
      refetchDocs()
    }
  }

  const onLinkError = async (error: Error) => {
    const responseError = error as ResponseError
    const message = await responseError?.response?.json()

    if (message?.detail === errorsMap.uploadLinkAlreadyRunning) {
      toast.info(t('confluenceLinkJobAlreadyRunning'), {
        description: (
          <div>
            <Link
              to={{
                pathname: '/settings/tasks-status',
              }}
              className="underline"
            >
              {t('checkStatusOfUpload')}
            </Link>
          </div>
        ),
        duration: 8000,
      })
    } else {
      toast.error(t('errors.failedToUploadLink'))
    }
  }

  const validateUrl = (url: string) => {
    try {
      new URL(url)
      return true
    } catch (_) {
      return false
    }
  }

  const resetUrls = () => {
    setUrlFields([{ id: crypto.randomUUID(), value: '' }])
    setTouchedFields(new Set())
    setProcessAsOne(false)
  }

  const addUrlField = () => {
    setUrlFields([...urlFields, { id: crypto.randomUUID(), value: '' }])
  }

  const removeUrlField = (id: string) => {
    if (urlFields.length > 1) {
      const newUrlFields = urlFields.filter((field) => field.id !== id)
      setUrlFields(newUrlFields)
      const newTouchedFields = new Set(touchedFields)
      newTouchedFields.delete(id)
      setTouchedFields(newTouchedFields)
    }
  }

  const handleUrlChange = (id: string, value: string) => {
    const newUrlFields = urlFields.map((field) =>
      field.id === id ? { ...field, value } : field
    )
    setUrlFields(newUrlFields)
  }

  const handleUrlBlur = (id: string) => {
    const newTouchedFields = new Set(touchedFields)
    newTouchedFields.add(id)
    setTouchedFields(newTouchedFields)
  }

  const isValidUrlForField = (field: UrlField) => {
    return field.value.trim() === '' || validateUrl(field.value)
  }

  const hasValidUrls = () => {
    return urlFields.some(
      (field) => field.value.trim() !== '' && validateUrl(field.value)
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold capitalize">
            {t(title)}
          </DialogTitle>
        </DialogHeader>
        {!mode && (
          <div className="flex flex-col items-center gap-4 my-6">
            <Button
              dataTestId="upload-file"
              variant="outline"
              className="capitalize font-semibold flex items-center gap-2"
              onClick={() => {
                setMode('file')
              }}
            >
              <PdfIcon />
              {t('uploadFile')}
            </Button>
            {confluenceLinkDesignReview && (
              <Button
                dataTestId="confluence-link"
                variant="outline"
                className="capitalize font-semibold flex items-center gap-2"
                onClick={() => setMode('link')}
              >
                <ConfluenceIcn />
                {t('confluenceLink')}
              </Button>
            )}
            {googleLinkDesignReview && (
              <Button
                dataTestId="google-link"
                variant="outline"
                className="capitalize font-semibold flex items-center gap-2"
                onClick={() => setMode('gDoc')}
              >
                <GoogleDocIcon />
                {t('googleDocLink')}
              </Button>
            )}
          </div>
        )}
        {(mode === 'link' || mode === 'gDoc') && (
          <div className="flex flex-col gap-4">
            <p className="text-sm text-muted-foreground">
              {t('uploadLinkDescription')}
            </p>
            <p className="font-semibold capitalize">
              {mode === 'link' ? t('confluence') : t('googleDoc')}
            </p>

            <div className="space-y-3">
              {urlFields.map((field) => (
                <div key={field.id} className="flex gap-2 items-center">
                  <div className="flex-1">
                    <Input
                      value={field.value}
                      onChange={(e) =>
                        handleUrlChange(field.id, e.target.value)
                      }
                      onBlur={() => handleUrlBlur(field.id)}
                      type="url"
                      placeholder={
                        mode === 'link'
                          ? t('confluenceLinkPlaceholder')
                          : t('googleLinkPlaceholder')
                      }
                    />
                    {touchedFields.has(field.id) &&
                      !isValidUrlForField(field) &&
                      field.value.trim() !== '' && (
                        <p className="text-red-500 text-xs mt-1">
                          {t('invalidUrl')}
                        </p>
                      )}
                  </div>
                  {urlFields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeUrlField(field.id)}
                      className="px-2"
                      dataTestId="remove-url-button"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addUrlField}
                className="w-fit"
                dataTestId="add-url-button"
              >
                <Plus className="h-4 w-4 mr-2" />
                {t('add')}{' '}
                {mode === 'link' ? t('confluenceLink') : t('googleDocLink')}
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="process-as-one-urls"
                checked={processAsOne}
                onCheckedChange={(checked) =>
                  setProcessAsOne(checked as boolean)
                }
                disabled={
                  urlFields.filter((field) => field.value.trim() !== '')
                    .length <= 1
                }
              />
              <label
                htmlFor="process-as-one-urls"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {t('analyzeAsOne')}
              </label>
            </div>

            <div className="flex items-center justify-end gap-2">
              <Button
                dataTestId="back"
                className="capitalize"
                variant="outline"
                onClick={() => {
                  setMode(null)
                  resetUrls()
                }}
              >
                {t('back')}
              </Button>
              <Button
                dataTestId="done-link-upload"
                disabled={!hasValidUrls() || isUrlPending}
                onClick={() => handleLinkUpload()}
              >
                {isUrlPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {t('done')}
              </Button>
            </div>
          </div>
        )}
        {mode === 'file' && (
          <DocumentUpload
            open={open}
            onOpenChange={onOpenChange}
            onUpload={handleUpload}
            isPending={uploadPending}
            maxFileSize={MAX_SIZE}
            showAnalyzeAsOne
          />
        )}
      </DialogContent>
    </Dialog>
  )
}
