import { toast } from 'sonner'
import {
  Button,
  Checkbox,
  Command,
  CommandInput,
  CommandItem,
  CommandList,
  Input,
} from '@libs/ui'
import { t } from 'i18next'
import { z } from 'zod'
import type { QueryView } from 'prime-front-service-client'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  getParsedArrayOfObjects,
  getStringifiedArrayOfObjects,
} from '../../../router/router'
import {
  useAddFilterPresets,
  useAddOrUpdateQueryCasesView,
  useGetWorkroomFields,
} from '../../../api/use-config-api'
import { RulesFiltersForm } from './rule-filters-form'
import { Loader2 } from 'lucide-react'
import { ReviewRule } from './review-rule'
import type { FilterItem } from '@libs/ui'
import { mergeDuplicateFilters } from './utils'

export const filterItemSchema = z.object({
  field: z.string(),
  op: z.string(),
  value: z.union([z.string(), z.array(z.string())]),
})

export const ruleFormSchema = z.object({
  name: z.string().min(3, 'Name must contain at least 3 character(s)'),
  projects: z.array(z.string()),
  filters: z.array(filterItemSchema).optional(),
})

const isFilterValid = (filter: FilterItem) => {
  return (
    filter.field !== '' &&
    filter.op !== '' &&
    filter.value !== '' &&
    filter.value !== null &&
    filter.value !== undefined &&
    (Array.isArray(filter.value) ? filter.value.length > 0 : true)
  )
}

interface RuleFormProps {
  rule: QueryView | null
  updateSuccess: () => void
  back: () => void
}
export const RuleForm = ({ rule, updateSuccess, back }: RuleFormProps) => {
  const [step, setStep] = useState<'projects' | 'filters' | 'review'>(
    'projects'
  )

  const { data: workroomFields, isPending: pendingWorkroomFields } =
    useGetWorkroomFields(false)

  const useAddFilterPresetsMutation = useAddFilterPresets()
  const useUpdateFilterPresetsMutation = useAddOrUpdateQueryCasesView()

  const projectsField = workroomFields?.find((f) => f.name === 'Project')
  const projectsOptions = projectsField?.options ?? []

  const ruleForm = useForm<z.infer<typeof ruleFormSchema>>({
    resolver: zodResolver(ruleFormSchema),
    defaultValues: {
      name: rule?.name || '',
      projects:
        getParsedArrayOfObjects(rule?.query_list)?.find(
          (item) => item.field === 'provider_fields.project'
        )?.value || [],
      filters: getParsedArrayOfObjects(rule?.query_list) || [],
    },
  })

  const selectedProjects = ruleForm.watch('projects') ?? []

  const selectedFilters = ruleForm.watch('filters') ?? []

  useEffect(() => {
    if (workroomFields)
      workroomFields.push({
        type: 'string',
        id: 'parent_id',
        name: 'Parent Id',
      })
  }, [workroomFields])

  const handleProjectToggle = (project: string) => {
    const current = new Set(selectedProjects)
    if (current.has(project)) {
      current.delete(project)
    } else {
      current.add(project)
    }
    ruleForm.setValue('projects', Array.from(current))
  }

  const addRule = () => {
    if (rule) {
      updateRule()
    } else {
      addNewRule()
    }
  }

  const updateRule = async () => {
    const ruleValues = ruleForm.getValues()
    const filters = (ruleValues.filters as FilterItem[]).filter(isFilterValid)

    const combinedFilters = mergeDuplicateFilters([
      ...filters,
      {
        field: 'provider_fields.project',
        op: 'eq',
        value: [...ruleValues.projects],
      },
    ])

    try {
      await useUpdateFilterPresetsMutation.mutateAsync({
        queryView: {
          name: ruleValues.name || '',
          query_id: rule?.query_id || '',
          query_list: getStringifiedArrayOfObjects(combinedFilters) || [],
        },
        view_type: 'design_reviews',
      })
      updateSuccess()
      toast.success(t('ruleSavedSuccessfully'))
    } catch (error) {
      console.error('Failed to update rule:', error)
      toast.error(t('errors.failedToUpdateRule'))
    }
  }

  const addNewRule = () => {
    const query_id = crypto.randomUUID().split('-')[0]
    const ruleValues = ruleForm.getValues()
    const filters = (ruleValues.filters as FilterItem[]).filter(isFilterValid)

    const combinedFilters = mergeDuplicateFilters([
      ...filters,
      {
        field: 'provider_fields.project',
        op: 'eq',
        value: [...ruleValues.projects],
      },
    ])

    useAddFilterPresetsMutation.mutate(
      {
        queryView: {
          name: ruleValues.name || '',
          query_id,
          query_list: getStringifiedArrayOfObjects(combinedFilters) || [],
        },
        view_type: 'design_reviews',
      },
      {
        onSuccess: async () => {
          updateSuccess()
          toast.success(t('ruleCreatedSuccessfully'))
        },
        onError: () => {
          toast.error(t('errors.failedToCreateRule'))
        },
      }
    )
  }

  return (
    <div className="flex flex-col gap-4">
      {step === 'projects' && (
        <>
          <div className="my-4">
            <Input
              value={ruleForm.watch('name')}
              onChange={(e) => ruleForm.setValue('name', e.target.value)}
              placeholder={t('ruleName')}
              className="w-full"
            />
          </div>
          {pendingWorkroomFields ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Command className="border rounded-md max-h-[300px] overflow-auto">
              <CommandInput placeholder="Search projects..." />
              <CommandList>
                {projectsOptions.map((project) => (
                  <CommandItem
                    key={project}
                    onSelect={() => handleProjectToggle(project)}
                  >
                    <Checkbox
                      checked={selectedProjects.includes(project)}
                      className="mr-2"
                    />
                    {project}
                  </CommandItem>
                ))}
              </CommandList>
            </Command>
          )}
          <div className="flex justify-end gap-2 mt-4">
            <Button
              className="capitalize"
              dataTestId="back-button"
              variant="secondary"
              onClick={back}
            >
              {t('back')}
            </Button>
            <Button
              disabled={!selectedProjects.length}
              dataTestId="next-button"
              className="capitalize"
              onClick={() => {
                setStep('filters')
              }}
            >
              {t('next')}
            </Button>
          </div>
        </>
      )}
      {step === 'filters' && (
        <>
          <RulesFiltersForm
            fields={workroomFields || []}
            filters={
              selectedFilters.length
                ? selectedFilters
                : [{ field: '', op: 'eq', value: '' }]
            }
            onFiltersChange={(updatedFilters) => {
              ruleForm.setValue('filters', updatedFilters)
            }}
          />

          <div className="flex justify-end gap-2 mt-4">
            <Button
              className="capitalize"
              dataTestId="back-button"
              variant="secondary"
              onClick={() => setStep('projects')}
            >
              {t('back')}
            </Button>
            <Button
              className="capitalize"
              dataTestId="save-button"
              onClick={() => {
                setStep('review')
              }}
            >
              {t('reviewRule')}
            </Button>
          </div>
        </>
      )}
      {step === 'review' && (
        <ReviewRule
          fields={workroomFields || []}
          ruleName={ruleForm.watch('name')}
          projects={ruleForm.watch('projects')}
          filters={(ruleForm.watch('filters') as FilterItem[]) ?? []}
          back={() => setStep('filters')}
          addRule={addRule}
        />
      )}
    </div>
  )
}
