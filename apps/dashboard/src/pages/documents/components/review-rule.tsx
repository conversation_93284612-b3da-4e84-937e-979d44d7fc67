import {
  <PERSON><PERSON>,
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  TableSkeleton,
  ActiveFilterBadge,
} from '@libs/ui'
import type { FilterItem } from '@libs/ui'
import { t } from 'i18next'
import { useGetCasesForAccount } from '../../../api/use-cases-api'
import { useCallback } from 'react'
import type { ProviderFieldInfoOptions } from 'prime-front-service-client'
import { mergeDuplicateFilters } from './utils'

interface ReviewRuleProps {
  ruleName: string
  projects: string[]
  fields: ProviderFieldInfoOptions[]
  filters: FilterItem[]
  addRule: () => void
  back: () => void
}

export const ReviewRule = ({
  ruleName,
  projects,
  fields,
  filters,
  addRule,
  back,
}: ReviewRuleProps) => {
  const mergedFilters = mergeDuplicateFilters([
    ...filters,
    {
      field: 'provider_fields.project',
      op: 'eq',
      value: [...projects],
    },
  ])

  const { data, isPending } = useGetCasesForAccount({
    limit: 10,
    offset: 0,
    filters: mergedFilters.map((filter) => JSON.stringify(filter)),
    sort: [
      JSON.stringify({ field: 'provider_fields.created', direction: 'desc' }),
    ],
  })

  const getFilterTitle = useCallback(
    (key: string) => {
      return fields.find((field) => field.id === key)?.name || key
    },
    [filters]
  )

  return (
    <div className="flex flex-col gap-3 p-4">
      <div>
        <h2 className="text-lg font-semibold">{ruleName}</h2>
      </div>

      <div className="flex items-center gap-3 flex-wrap">
        {mergedFilters.map((item) => {
          return (
            item.field && (
              <ActiveFilterBadge
                key={item.field}
                title={getFilterTitle(item.field)}
                id={item.field}
                isDate={item.op === 'between'}
                getFilter={() => item}
                enableDelete={false}
                deleteFilter={() => {
                  return
                }}
              />
            )
          )
        })}
      </div>
      <div className="text-sm my-2">10 last results, you might have more</div>

      <div className="bg-white h-[400px] overflow-auto">
        {isPending ? (
          <TableSkeleton />
        ) : !data?.results?.length ? (
          <div>
            <div className="text-center">
              <div>{t('noResultsFound')}</div>
            </div>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow className="capitalize">
                <TableHead className="px-4 py-3 w-72">{t('title')}</TableHead>
                <TableHead className="px-4 py-3">{t('ticketId')}</TableHead>
                <TableHead className="px-4 py-3">{t('created')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.results?.map((caseItem) => (
                <TableRow key={crypto.randomUUID()}>
                  <TableCell className="p-3 text-muted-foreground capitalize">
                    {caseItem.title}
                  </TableCell>
                  <TableCell className="p-3 text-muted-foreground capitalize">
                    {caseItem.issue_id}
                  </TableCell>
                  <TableCell className="p-3 text-muted-foreground capitalize">
                    {caseItem?.provider_fields?.created &&
                      new Date(
                        caseItem.provider_fields.created
                      ).toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
      <div className="flex justify-end mt-4 gap-2">
        <Button
          className="capitalize"
          dataTestId="back-button"
          variant="secondary"
          onClick={back}
        >
          {t('back')}
        </Button>
        <Button onClick={addRule} dataTestId="save-rule" className="capitalize">
          {t('apply')}
        </Button>
      </div>
    </div>
  )
}
