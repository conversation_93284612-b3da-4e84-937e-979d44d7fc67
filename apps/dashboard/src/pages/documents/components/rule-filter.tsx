import type { ProviderFieldInfoOptions } from 'prime-front-service-client'
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  DateRangePicker,
} from '@libs/ui'
import type { FilterItem, FilterOperator } from '@libs/ui'
import { EqualIcon, EqualNotIcon, SearchIcon, XIcon } from 'lucide-react'
import { t } from 'i18next'
import { useEffect, useState } from 'react'
import { MultiSelectFilter } from './multi-select-filter'

interface RuleFilterProps {
  selectedFilter?: FilterItem | null
  fields: ProviderFieldInfoOptions[]
  onFilterChange: (filter: FilterItem) => void
}

export const RuleFilter = ({
  selectedFilter,
  fields,
  onFilterChange,
}: RuleFilterProps) => {
  const [filter, setFilter] = useState<FilterItem | null>(
    selectedFilter || null
  )
  const [type, setType] = useState<string | null>(null)
  const [filterOperator, setFilterOperator] = useState(
    selectedFilter?.op || 'eq'
  )
  const [searchQuery, setSearchQuery] = useState(
    (filter?.value as string) || ''
  )
  const onOperatorChange = (op: FilterOperator) => {
    setFilterOperator(op)
    if (selectedFilter) {
      onFilterChange({
        field: filter?.field || '',
        op,
        value: filter?.value || '',
      })
    }
  }

  useEffect(() => {
    if (filter?.field) {
      const fieldType = fields.find((f) => f.id === filter.field)?.type || ''
      setType(fieldType)
    }
  }, [filter?.field, fields])

  const updateFilter = (updatedFilter: FilterItem) => {
    setFilter(updatedFilter)
    onFilterChange(updatedFilter)
  }

  const onFieldChange = (field: string) => {
    const newType = fields.find((f) => f.id === field)?.type || ''
    setFilter({
      field,
      op: filterOperator,
      value:
        newType === 'date'
          ? ''
          : newType === 'enum' || newType === 'array'
          ? []
          : '',
    })
    setType(newType)
    if (newType === 'string') {
      setFilterOperator('eq')
    }
  }

  return (
    <div className="grid grid-flow-col grid-cols-[1fr_1fr_1fr_20px] gap-2 items-center justify-between">
      <Select
        value={filter?.field ?? ''}
        onValueChange={(value) => onFieldChange(value)}
      >
        <SelectTrigger className="w-44">
          <SelectValue placeholder={t('pickFilter')} />
        </SelectTrigger>
        <SelectContent>
          {fields
            ?.filter((field) => field.id !== 'provider_fields.project')
            .map((field) => (
              <SelectItem key={field.id} value={field.id}>
                {field.name}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>

      {type === 'date' ? (
        <Select disabled>
          <SelectTrigger className="capitalize">{t('between')}</SelectTrigger>
        </Select>
      ) : type === 'string' ? (
        <Select disabled>
          <SelectTrigger className="capitalize">{t('contains')}</SelectTrigger>
        </Select>
      ) : (
        <Select
          value={filterOperator}
          onValueChange={(value) => onOperatorChange(value as FilterOperator)}
        >
          <SelectTrigger className="w-44">
            <SelectValue />
          </SelectTrigger>
          <SelectContent align="center">
            <SelectItem value="eq">
              <div className="flex gap-1 items-center">
                <EqualIcon size={12} /> {t('eq')}
              </div>
            </SelectItem>
            <SelectItem value="ne">
              <div className="flex gap-1 items-center">
                <EqualNotIcon size={12} /> {t('ne')}
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      )}

      <div className="flex-1">
        {!filter ? (
          <Select disabled>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Value" />
            </SelectTrigger>
          </Select>
        ) : type === 'date' ? (
          <DateRangePicker
            filterKey={filter.field}
            savedFilter={filter?.value ? filter : undefined}
            onFilterChange={(filter) => {
              setFilter(filter)
              onFilterChange(filter)
            }}
          />
        ) : type === 'enum' || type === 'array' ? (
          <MultiSelectFilter
            id={filter.field}
            filterOperator={filterOperator}
            fields={fields ?? []}
            savedFilter={filter.value ? filter : undefined}
            onFilterChange={(filter) => {
              setFilter(filter)
              onFilterChange(filter)
            }}
            withSearch
          />
        ) : (
          <div className="relative w-44">
            <SearchIcon className="absolute left-2.5 top-[8px] h-4 w-4 text-muted-foreground" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onBlur={() => {
                if (
                  searchQuery !== filter?.value ||
                  filterOperator !== filter?.op
                ) {
                  updateFilter({
                    field: filter?.field,
                    value: searchQuery || '',
                    op: filterOperator,
                  })
                }
              }}
              className="h-8 bg-white pl-8"
              placeholder="Search here..."
            />
            {searchQuery && (
              <Button
                variant="ghost"
                className="absolute capitalize text-muted-foreground right-2.5 top-[-1px]  bg-transparent px-0"
                dataTestId="clear-search"
                onClick={() => {
                  setSearchQuery('')
                  onFilterChange({
                    field: filter?.field,
                    value: '',
                    op: filterOperator,
                  })
                }}
              >
                <XIcon className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
