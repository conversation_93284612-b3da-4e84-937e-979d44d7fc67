import type { FilterItem, FilterOperator } from '@libs/ui'
import { useUrlState } from '@libs/ui'
import type { UrlStateProps } from './use-selected-columns'
import { useUrlStateDefaultOptions } from './use-selected-columns'
import { useCallback, useMemo } from 'react'
import { atom, useAtom } from 'jotai'
import { usePageReset } from './use-page-reset'

const pendingFiltersAtom = atom<FilterItem[]>([])

export const useFilters = () => {
  const [state, setState] = useUrlState<UrlStateProps>(
    {},
    {
      ...useUrlStateDefaultOptions,
    }
  )

  const { resetPage } = usePageReset()

  const filters = useMemo((): FilterItem[] => {
    return state.f || []
  }, [state.f])

  const stringifiedFilters = useMemo(() => {
    return state.f?.length
      ? state.f.map((filter: FilterItem) => JSON.stringify(filter))
      : []
  }, [state.f])

  const updateFilter = useCallback(
    (newFilter: FilterItem) => {
      setState((prevState) => {
        const currentFilters = [...(prevState.f || [])]
        const existingFilterIndex = currentFilters.findIndex(
          (filter) => filter.field === newFilter.field
        )

        if (existingFilterIndex === -1) {
          // If the filter doesn't exist, add it
          return {
            ...prevState,
            f: [...currentFilters, newFilter],
          }
        }

        const existingFilter = currentFilters[existingFilterIndex]

        // Update the operator if it has changed
        const updatedOp = newFilter.op || existingFilter.op

        // If it's not an array (single-value filter), replace the value
        if (!Array.isArray(newFilter.value)) {
          currentFilters[existingFilterIndex] = {
            ...existingFilter,
            op: updatedOp,
            value: newFilter.value,
          }
          return {
            ...prevState,
            f: currentFilters,
          }
        }

        // Handle array-based (multi-select) values
        const newValues = newFilter.value as string[]
        let updatedValue = existingFilter.value.slice() as string[] // Copy existing values

        // If the new values array differs from the existing, replace it entirely
        if (JSON.stringify(newValues) !== JSON.stringify(updatedValue)) {
          updatedValue = newValues
        } else {
          // Toggle the values if they match
          newValues.forEach((val) => {
            const index = updatedValue.indexOf(val)
            if (index === -1) {
              // Add the value if it doesn't exist
              updatedValue.push(val)
            } else {
              // Remove the value if it exists
              updatedValue.splice(index, 1)
            }
          })
        }

        if (updatedValue.length === 0) {
          // Remove the filter if the resulting value is empty
          currentFilters.splice(existingFilterIndex, 1)
        } else {
          currentFilters[existingFilterIndex] = {
            ...existingFilter,
            op: updatedOp,
            value: updatedValue,
          }
        }

        return {
          ...prevState,
          f: currentFilters.length > 0 ? currentFilters : undefined,
        }
      })
      resetPage()
    },
    [setState, resetPage]
  )

  const deleteFilter = useCallback(
    (field: string, op: FilterOperator, isSearch: boolean) => {
      const newFilters = [...(state.f || [])].filter((f) => {
        if (isSearch) {
          return f.field !== field
        }
        return f.field !== field || f.op !== op
      })
      setState((prevState) => ({
        ...prevState,
        f: newFilters.length > 0 ? newFilters : undefined,
      }))
      resetPage()
    },
    [state.f, setState]
  )

  const updateFilters = useCallback(
    (newFilters: FilterItem[]) => {
      newFilters.forEach((newFilter) => {
        if (newFilter.value.length) {
          updateFilter(newFilter)
        } else {
          deleteFilter(newFilter.field, newFilter.op as FilterOperator, false)
        }
      })
      resetPage()
    },
    [deleteFilter, updateFilter]
  )

  const getFilter = useCallback(
    (field: string): FilterItem | undefined => {
      const parsedFilters = state.f || []
      return parsedFilters.find((f: FilterItem) => f.field === field)
    },
    [state.f]
  )

  const deleteFilters = useCallback(() => {
    filters.forEach((filter) =>
      deleteFilter(filter.field, filter.op as FilterOperator, false)
    )
  }, [deleteFilter, filters])

  const clearFilters = useCallback(() => {
    setState((prevState) => ({
      ...prevState,
      f: undefined,
    }))
  }, [setState])

  ////////////////////////////////////////
  // pending filters  //
  ////////////////////////////////////////

  const [pendingFilters, setPendingFilters] = useAtom(pendingFiltersAtom)

  const clearPendingFilters = useCallback(() => {
    setPendingFilters([])
  }, [setPendingFilters])

  const updatePendingFilters = useCallback(
    (newFilter: FilterItem) => {
      if (newFilter.value.length === 0) {
        setPendingFilters((prev) => {
          return prev.filter((f) => f.field !== newFilter.field)
        })
        return
      }

      setPendingFilters((prev) => {
        const newFilters = prev.filter((f) => f.field !== newFilter.field)
        return [...newFilters, newFilter]
      })
    },
    [setPendingFilters]
  )

  const applyPendingFilters = useCallback(() => {
    setState((prevState) => ({
      ...prevState,
      f: pendingFilters.length > 0 ? pendingFilters : undefined,
    }))
    clearPendingFilters()
    resetPage()
  }, [setState, pendingFilters, clearPendingFilters])

  const getPendingFilter = useCallback(
    (field: string) => {
      return pendingFilters.find((f) => f.field === field)
    },
    [pendingFilters]
  )

  return {
    filters,
    deleteFilters,
    stringifiedFilters,
    updateFilter,
    updateFilters,
    getFilter,
    deleteFilter,
    clearFilters,
    pendingFilters,
    getPendingFilter,
    updatePendingFilters,
    clearPendingFilters,
    applyPendingFilters,
    setPendingFilters,
  }
}
