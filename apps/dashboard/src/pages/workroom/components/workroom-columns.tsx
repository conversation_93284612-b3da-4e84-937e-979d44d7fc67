/*eslint-disable max-lines*/
import type { ColumnDef } from '@tanstack/react-table'
import { createColumnHelper } from '@tanstack/react-table'
import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { Link } from 'react-router'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  Badge,
  Checkbox,
  SortRenderer,
} from '@libs/ui'

import { t } from 'i18next'
import {
  StatusUpdateCell,
  RiskScoreCell,
  TitleCell,
  ContainerCell,
} from './table-cells'

export const columnHelper = createColumnHelper<ExternalCaseWorkroom>()

export const permanentColumns = [
  'select',
  'issue_id',
  'title',
  'parents',
  'mitre_categories',
  'linddun_categories',
  'risk_score_category',
  'status',
  'is_automated',
  'is_security_enhancement',
  'provider_fields.project',
  'provider_fields.creator',
  'provider_fields.created',
]

const labelClassName =
  'rounded-full text-gray-400 border-gray-400 h-6 bg-white font-light gap-2'

export const staticColumns = [...permanentColumns]

export const columnsWithStaticOrder = ['select']

const ColumnHeader = ({
  title,
  content,
}: {
  title: string
  content: string
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className="flex items-center gap-2 font-bold">{title}</div>
      </TooltipTrigger>
      <TooltipContent>{content}</TooltipContent>
    </Tooltip>
  )
}

const staticTooltipContent = {
  title: t('workroomPage.importedFromJira'),
  status: t('workroomPage.caseStatusInPrime'),
  risk_score_category: t('workroomPage.indicatesTheLevelOfPotentialRisk'),
  issue_id: t('workroomPage.jiraTicketID'),
  labels: t('workroomPage.labels'),
  fire_summary: t('abstractTooltip'),
  mitre_categories: t('mitreTooltip'),
  linddun_categories: t('lindunnTooltip'),
}

export const workroomColumns = [
  columnHelper.display({
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        className="mx-2"
        checked={
          table.getIsAllRowsSelected() ||
          (table.getIsSomeRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) =>
          table.getRowModel().rows.forEach((row) => row.toggleSelected(!!value))
        }
        aria-label="Select all"
      />
    ),
    size: 50,
    cell: ({ row }) => (
      <Checkbox
        className="mx-2"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    footer: (info) => info.column.id,
  }),
  columnHelper.accessor('issue_id', {
    id: 'issue_id',
    header: ({ column }) =>
      SortRenderer({
        title: t('tableColumns.issueId'),
        column,
        tooltip: staticTooltipContent.issue_id,
        staticColumns: permanentColumns,
      }),
    size: 100,
    cell: (info) => (
      <Link
        to={`/workroom/${info.row.original.source_id}/${info.row.original.issue_id}`}
        className="hover:underline"
      >
        <span>{info.getValue()}</span>
      </Link>
    ),
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.display({
    header: () => (
      <ColumnHeader
        title={t('tableColumns.title')}
        content={staticTooltipContent.title}
      />
    ),
    id: 'title',
    size: 300,
    cell: (info) => {
      return <TitleCell caseInfo={info.row.original} />
    },
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.display({
    id: 'parents',
    header: ({ column }) =>
      SortRenderer({
        title: t('container'),
        column,
        staticColumns: permanentColumns,
      }),
    size: 100,
    cell: (info) => <ContainerCell caseInfo={info.row.original} />,
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.accessor('issue_analysis.mitre_categories', {
    header: ({ column }) =>
      SortRenderer({
        title: t('tableColumns.mitre_categories'),
        column,
        enableSort: false,
        tooltip: staticTooltipContent.mitre_categories,
        staticColumns: permanentColumns,
      }),
    id: 'mitre_categories',
    size: 300,
    cell: (info) => {
      const mitre_categories = info.getValue()
      return (
        <div className="flex flex-wrap items-center gap-1 w-[300px]">
          {mitre_categories &&
            mitre_categories.map((cat) => (
              <Badge
                key={crypto.randomUUID()}
                className="whitespace-nowrap text-slate-500 bg-slate-100 border-none font-light shadow-none hover:bg-gray-100"
              >
                {cat}
              </Badge>
            ))}
        </div>
      )
    },

    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.accessor('issue_analysis.linddun_categories', {
    header: ({ column }) =>
      SortRenderer({
        title: t('tableColumns.linddun_categories'),
        column,
        tooltip: staticTooltipContent.linddun_categories,
        enableSort: false,
        staticColumns: permanentColumns,
      }),
    id: 'linddun_categories',
    size: 300,
    cell: (info) => {
      const linddun_categories = info.getValue()
      return (
        <div className="flex flex-wrap items-center gap-1 w-[300px]">
          {linddun_categories &&
            linddun_categories.map((cat) => (
              <Badge
                key={crypto.randomUUID()}
                className="whitespace-nowrap text-slate-500 bg-slate-100 border-none font-light shadow-none hover:bg-gray-100"
              >
                {cat}
              </Badge>
            ))}
        </div>
      )
    },

    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.display({
    header: ({ column }) =>
      SortRenderer({
        title: t('tableColumns.riskScoreCategory'),
        column,
        tooltip: staticTooltipContent.risk_score_category,
        staticColumns: permanentColumns,
      }),
    id: 'risk_score_category',
    size: 100,
    cell: (info) => <RiskScoreCell caseInfo={info.row.original} />,
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.display({
    id: 'status',
    header: ({ column }) =>
      SortRenderer({
        title: t('tableColumns.status'),
        column,
        tooltip: staticTooltipContent.status,
        staticColumns: permanentColumns,
      }),
    size: 100,
    cell: (info) => <StatusUpdateCell caseInfo={info.row.original} />,
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.accessor('provider_fields', {
    id: 'provider_fields.project',
    header: ({ column }) =>
      SortRenderer({
        title: t('tableColumns.project'),
        column,
        staticColumns: permanentColumns,
      }),
    size: 150,
    cell: (info) => {
      const providerFields = info.getValue()
      return <div>{providerFields.project && providerFields.project}</div>
    },

    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.accessor('provider_fields', {
    id: 'provider_fields.creator',
    header: ({ column }) =>
      SortRenderer({
        title: t('tableColumns.creator'),
        column,
        staticColumns: permanentColumns,
      }),
    size: 150,
    cell: (info) => {
      const providerFields = info.getValue()
      return <div>{providerFields.creator && providerFields.creator}</div>
    },

    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.accessor('provider_fields', {
    id: 'provider_fields.created',
    header: ({ column }) =>
      SortRenderer({
        title: t('tableColumns.created'),
        column,
        staticColumns: permanentColumns,
      }),
    size: 150,
    cell: (info) => {
      const providerFields = info.getValue()
      return (
        <div>
          {providerFields.created &&
            new Date(providerFields.created).toLocaleDateString()}
        </div>
      )
    },

    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,

  columnHelper.accessor('labels', {
    header: ({ column }) =>
      SortRenderer({
        column,
        title: t('tableColumns.labels'),
        tooltip: t('tableColumns.labels'),
        staticColumns: permanentColumns,
      }),
    id: 'labels',
    cell: (info) => {
      const labels = info.getValue()
      return (
        <div className="flex flex-wrap gap-1 text-muted-foreground min-w-52">
          {labels.length > 2 ? (
            <div key={crypto.randomUUID()}>
              <Badge variant="secondary" className={labelClassName}>
                {labels[0]}
              </Badge>
              <Badge variant="secondary" className={labelClassName}>
                {labels[1]}
              </Badge>

              <Tooltip>
                <TooltipTrigger>
                  <Badge variant="secondary" className={labelClassName}>
                    + {labels.length - 2}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent
                  side="left"
                  className="w-full max-w-60 gap-2 flex flex-wrap"
                >
                  {labels.map((label, index) => (
                    <div key={crypto.randomUUID()}>
                      {label}
                      {labels.length - 1 === index ? '' : ','}{' '}
                    </div>
                  ))}
                </TooltipContent>
              </Tooltip>
            </div>
          ) : (
            labels.map((label) => (
              <Badge
                key={crypto.randomUUID()}
                variant="secondary"
                className={labelClassName}
              >
                {label}
              </Badge>
            ))
          )}
        </div>
      )
    },
    footer: (info) => info.column.id,
  }) as ColumnDef<ExternalCaseWorkroom, unknown>,
]
