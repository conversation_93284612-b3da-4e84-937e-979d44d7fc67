import { riskScoreCategoryToClass, StateBadge } from '@libs/ui'
import type { CaseStatus } from 'prime-front-service-client'
import { cn } from '@libs/common'
import { ALLOWED_FILTERS } from '../utils'

interface FilterOptionProps {
  option: string
  id: string
}

export function FilterOption({ option, id }: FilterOptionProps) {
  switch (id) {
    case ALLOWED_FILTERS.status:
      return <StateBadge state={option.toLowerCase() as CaseStatus} />
    case ALLOWED_FILTERS.risk_score_category:
      return (
        <div
          className={cn(
            'flex items-center justify-center gap-1 capitalize rounded-full h-8 w-28 font-bold',

            option === 'intervene'
              ? riskScoreCategoryToClass['intervene']
              : option === 'analyze'
              ? riskScoreCategoryToClass['analyze']
              : riskScoreCategoryToClass['monitor']
          )}
        >
          <div>{option}</div>
        </div>
      )
    default:
      return <span>{option}</span>
  }
}
