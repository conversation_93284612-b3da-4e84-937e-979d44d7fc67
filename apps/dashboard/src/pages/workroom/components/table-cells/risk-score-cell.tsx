import type { ExternalCaseWorkroom } from 'prime-front-service-client'
import { Badge } from '@libs/ui'

export const RiskScoreCell = ({
  caseInfo,
}: {
  caseInfo: ExternalCaseWorkroom
}) => {
  const riskScoreCategory = caseInfo.issue_analysis.risk_score_category
  return (
    <Badge
      size="lg"
      className="capitalize"
      variant={riskScoreCategory === 'None' ? 'analyze' : riskScoreCategory}
    >
      {caseInfo.issue_analysis.risk_score_category}
    </Badge>
  )
}
