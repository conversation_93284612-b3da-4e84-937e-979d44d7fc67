/* eslint-disable max-lines */
import type { AssessmentResult, Resources } from 'prime-front-service-client'
import {
  Button,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@libs/ui'
import { RiskScore, RiskTree } from './risk-tree'
import { cn } from '@libs/common'
import { ChevronRight, SquareArrowOutUpRightIcon } from 'lucide-react'
import { Fragment } from 'react'

const getColor = (score: number) => {
  if (score >= 70)
    return 'bg-red-100 text-red-600 border-2 border-red-300 hover:bg-red-200'
  if (score >= 30)
    return 'bg-orange-100 text-orange-600 border border-orange-600 hover:bg-orange-200'
  return 'bg-yellow-100 text-yellow-600 border border-yellow-600 hover:bg-yellow-200'
}

const ResourcePlaceholder = () => {
  return (
    <Fragment>
      <div className="p-2 text-muted-foreground font-normal border-b flex items-center"></div>
      <div className="p-2 text-muted-foreground font-normal border-b flex items-center"></div>
      <div className="p-2 border-b items-center"></div>
    </Fragment>
  )
}

export function ResourceTable({
  resources,
}: {
  resources: Resources | null | undefined
}) {
  const saasResourcesLength = resources?.saas_resources.length || 1
  const cloudResourcesLength = resources?.cloud_resources.length || 1
  const generalResourcesLength = resources?.general_resources.length || 1

  return (
    <div className="">
      <div className="max-w-4xl mx-auto bg-white">
        <div className="grid grid-cols-[auto_1fr_1fr_85px] gap-0 text-sm">
          <div className="p-2 font-bold border-b border-slate-500">
            Resource Category
          </div>
          <div className="p-2 font-bold border-b border-slate-500">
            Resource Type
          </div>
          <div className="p-2 font-bold border-b border-slate-500">
            Resource Name
          </div>

          <Tooltip>
            <TooltipTrigger asChild>
              <div className="p-2 font-bold border-b border-slate-500">
                BIA Score
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-[220px] bg-white text-slate-500 border">
              <p>
                Business Impact Analysis (BIA) score is calculated based on the
                potential impact on the Confidentiality, Integrity, and
                Availability of the resources.
              </p>
            </TooltipContent>
          </Tooltip>

          {/* SaaS Section */}
          <div
            className="p-2 font-semibold text-gray-700 border-b flex items-center"
            style={{
              gridRow: `span ${saasResourcesLength} / span ${saasResourcesLength}`,
            }}
          >
            SaaS
          </div>

          {resources?.saas_resources.length ? (
            resources?.saas_resources?.map((resource, index) => (
              <Fragment key={index}>
                <div className="p-2 text-muted-foreground font-normal border-b flex items-center">
                  {resource.type}
                </div>
                <div className="p-2 text-muted-foreground font-normal border-b flex items-center">
                  {resource.name}
                </div>
                <div className="p-2 border-b flex items-center">
                  <RiskScore score={resource.impact_score} />
                </div>
              </Fragment>
            ))
          ) : (
            <ResourcePlaceholder />
          )}

          {/* PaaS / IaaS */}
          <div
            className="p-2 font-semibold text-gray-700 border-b flex items-center"
            style={{
              gridRow: `span ${cloudResourcesLength} / span ${cloudResourcesLength}`,
            }}
          >
            PaaS / IaaS
          </div>

          {resources?.cloud_resources.length ? (
            resources?.cloud_resources?.map((resource, index) => (
              <Fragment key={index}>
                <div className="p-2 text-muted-foreground font-normal border-b flex items-center">
                  {resource.type}
                </div>
                <div className="p-2 text-muted-foreground font-normal border-b flex items-center">
                  {resource.name}
                </div>
                <div className="p-2 border-b items-center">
                  <RiskScore score={resource.impact_score} />
                </div>
              </Fragment>
            ))
          ) : (
            <ResourcePlaceholder />
          )}

          {/* Generic / Misc Section */}
          <div
            className="p-2 font-semibold text-gray-700 border-b flex items-center"
            style={{
              gridRow: `span ${generalResourcesLength} / span ${generalResourcesLength}`,
            }}
          >
            Generic / Misc
          </div>

          {resources?.general_resources.length ? (
            resources?.general_resources?.map((resource, index) => (
              <Fragment key={index}>
                <div className="p-2 text-muted-foreground font-normal border-b flex items-center">
                  {resource.type}
                </div>
                <div className="p-2 text-muted-foreground font-normal border-b flex items-center">
                  {resource.name}
                </div>
                <div className="p-2 border-b items-center">
                  <RiskScore score={resource.impact_score} />
                </div>
              </Fragment>
            ))
          ) : (
            <ResourcePlaceholder />
          )}
        </div>
      </div>
    </div>
  )
}

export function CollapsibleTables({
  assessment_result,
  resources,
}: {
  assessment_result: AssessmentResult | null | undefined
  resources: Resources | null | undefined
}) {
  const attackComplexityExplanation =
    assessment_result?.attack_complexity_explanation

  const exposurePresenceExplanation =
    assessment_result?.exposure_presence_explanation

  const thirdPartyPresenceExplanation =
    assessment_result?.third_party_presence_explanation

  const securityControlsPresenceExplanation =
    assessment_result?.security_controls_presence_explanation

  const factorsData = [
    {
      factor: 'Complexity',
      score: assessment_result?.attack_complexity,
      description: attackComplexityExplanation || '',
    },
    {
      factor: 'Public Exposure',
      score: assessment_result?.public_exposure,
      description: exposurePresenceExplanation || '',
    },
    {
      factor: 'Third Parties',
      score: assessment_result?.third_party,
      description: thirdPartyPresenceExplanation || '',
    },
    {
      factor: 'Compensating Controls',
      score: assessment_result?.security_controls,
      description: securityControlsPresenceExplanation || '',
    },
  ]

  return (
    <div className="p-6 space-y-8">
      <div className="grid grid-cols-[2fr_3fr] gap-8">
        {/* Factors Table */}
        <div className="space-y-4">
          <Table>
            <TableHeader>
              <TableRow className="border-slate-500">
                <TableHead className="font-bold">Factors</TableHead>
                <TableHead className="font-bold min-w-[110px] text-right">
                  Factor Score
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {factorsData.map((item) => (
                <TableRow key={item.factor}>
                  <TableCell className="p-0">
                    {item.description ? (
                      <Collapsible>
                        <CollapsibleTrigger className="group flex items-center gap-2 w-full p-4 text-left hover:bg-muted/50">
                          <ChevronRight className="h-4 w-4 transition-transform duration-400 group-data-[state=open]:rotate-90" />
                          <div>
                            <div className="">{item.factor}</div>
                            <div className="text-xs text-muted-foreground truncate group-data-[state=open]:hidden">
                              {item.description.substring(0, 30)}...
                            </div>
                          </div>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="px-10 pb-4">
                          <p className="text-xs text-muted-foreground">
                            {item.description}
                          </p>
                        </CollapsibleContent>
                      </Collapsible>
                    ) : (
                      <div className="p-4">{item.factor}</div>
                    )}
                  </TableCell>
                  <TableCell className="min-w-[110px] text-right">
                    <span className="text-base">{item.score}</span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Resources Table */}
        <div className="space-y-4">
          <ResourceTable resources={resources} />
        </div>
      </div>
    </div>
  )
}

export const PrimeRiskModal = ({
  risk_score,
  assessment_result,
  resources,
}: {
  risk_score: number
  assessment_result: AssessmentResult | null | undefined
  resources: Resources | null | undefined
}) => {
  return (
    <Dialog>
      <DialogTrigger disabled={!assessment_result || !resources}>
        <Button
          dataTestId="open-risk-details"
          size="sm"
          className={cn('gap-2', getColor(risk_score))}
        >
          <span className="text-slate-600">Prime Risk™</span>
          <span className="font-bold text-base">{risk_score}</span>
          {assessment_result && resources && (
            <SquareArrowOutUpRightIcon className="h-3 w-3 text-foreground" />
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="font-medium text-sm max-w-[1000px] w-full max-h-[90vh] overflow-y-auto">
        <DialogTitle>
          <span>Prime Risk™</span> Breakdown
        </DialogTitle>
        <RiskTree
          assessment_result={assessment_result}
          resources={resources}
          risk_score={risk_score}
        />
        <CollapsibleTables
          assessment_result={assessment_result}
          resources={resources}
        />
      </DialogContent>
    </Dialog>
  )
}
