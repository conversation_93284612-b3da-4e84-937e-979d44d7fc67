import {
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Label,
  RadioGroup,
  RadioGroupItem,
  Textarea,
} from '@libs/ui'
import { useUpdateCaseStatus } from '../../../api/use-cases-api'
import { t } from 'i18next'
import { ChevronDownIcon, CircleXIcon, Undo2Icon } from 'lucide-react'
import { CaseStatus, type ExternalCase } from 'prime-front-service-client'
import { useState } from 'react'
import { toast } from 'sonner'

export const CaseStatusDropdown = ({
  caseInfo,
  refetchCase,
  reopenCase,
}: {
  caseInfo: ExternalCase
  refetchCase: () => void
  reopenCase: () => void
}) => {
  const { mutate: updateCaseStatusMutation } = useUpdateCaseStatus()

  const [open, setOpen] = useState(false)
  const [radioValue, setRadioValue] = useState('')
  const [dismissReason, setDismissReason] = useState('')

  const handleRadioChange = (value: string) => {
    setRadioValue(value)
    if (value !== 'other') {
      setDismissReason(value)
    } else {
      setDismissReason('')
    }
  }

  const dismissCase = async () => {
    await handleUpdateStatus()
    setOpen(false)
  }

  const handleUpdateStatus = async () => {
    updateCaseStatusMutation(
      {
        source_id: caseInfo.source_id,
        issue_id: caseInfo.issue_id,
        status: CaseStatus.dismissed,
        dismissed_reason: dismissReason,
      },
      {
        onSuccess: () => {
          toast.success(t('riskScoreCategoryUpdatedSuccessfully'))
          refetchCase()
        },
        onError: () => {
          toast.error(t('errors.failedToUpdateRiskScoreCategory'))
        },
      }
    )
  }

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger>
          <Button
            dataTestId="case-status"
            variant="outline"
            className="capitalize gap-2 text-sm"
            size="sm"
          >
            {caseInfo.status}
            <ChevronDownIcon className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {caseInfo.status === 'dismissed' || caseInfo.status === 'done' ? (
            <DropdownMenuItem
              className="capitalize gap-2"
              onClick={() => reopenCase()}
            >
              <Undo2Icon className="h-4 w-4" />
              {t('reopen')}
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="capitalize gap-2"
              onClick={() => setOpen(true)}
            >
              <CircleXIcon size={18} />
              {t('dismiss')}
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          setOpen(isOpen)
          if (!isOpen) {
            setDismissReason('')
            setRadioValue('')
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px] font-medium text-sm">
          <DialogTitle>{t('whyWasItDismissed')}</DialogTitle>
          <RadioGroup value={radioValue} onValueChange={handleRadioChange}>
            <div className="flex items-center space-x-2 my-3">
              <RadioGroupItem
                value="Known issue/Already addressed"
                id="known-issue"
              />
              <Label htmlFor="known-issue">{t('knownIssue')}</Label>
            </div>
            <div className="flex items-center space-x-2 mb-3">
              <RadioGroupItem
                value="No security implications"
                id="no-security-implications"
              />
              <Label htmlFor="no-security-implications">
                {t('noSecurityImplications')}
              </Label>
            </div>
            <div className="flex items-center space-x-2 mb-3">
              <RadioGroupItem
                value="Opened by Security - Ignore"
                id="opened-by-security"
              />
              <Label htmlFor="opened-by-security">
                {t('openedBySecurity')}
              </Label>
            </div>
            <div className="flex items-start space-x-2 mb-3">
              <RadioGroupItem value="other" id="other" />
              <Label htmlFor="other">
                Other
                {radioValue === 'other' && (
                  <Textarea
                    className="my-4 p-4 w-full"
                    id="other-dismiss-reason"
                    placeholder={t('type')}
                    onChange={(e) => setDismissReason(e.target.value)}
                    disabled={radioValue !== 'other'}
                  />
                )}
              </Label>
            </div>
          </RadioGroup>
          <Button
            onClick={dismissCase}
            disabled={!dismissReason}
            dataTestId="submit-dismiss-button"
          >
            {t('submit')}
          </Button>
        </DialogContent>
      </Dialog>
    </div>
  )
}
