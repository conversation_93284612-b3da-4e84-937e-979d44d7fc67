import { cn } from '@libs/common'
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from '@libs/ui'
import { t } from 'i18next'
import type { AssessmentResult, Resources } from 'prime-front-service-client'

export const getRiskColor = (score: number) => {
  if (score >= 70) return 'bg-red-50 border-red-300'
  if (score >= 30) return 'bg-orange-50 border-orange-400'
  return 'bg-yellow-50 border-yellow-400'
}

const getSvgColor = (score: number) => {
  if (score >= 70) return '#FCA5A5'
  if (score >= 30) return '#FB923C'
  return '#FACC15'
}

export const RiskScore = ({ score }: { score: number }) => {
  return (
    <div
      className={cn(
        'font-bold text-center border-2 rounded-full w-8 h-8 flex items-center justify-center',
        getRiskColor(score)
      )}
    >
      {score}
    </div>
  )
}

const LeftLine = ({ color }: { color: string }) => {
  return (
    <svg
      width="243"
      height="30"
      viewBox="0 0 243 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M242.354 3.64645C242.549 3.84171 242.549 4.15829 242.354 4.35355L239.172 7.53553C238.976 7.7308 238.66 7.7308 238.464 7.53553C238.269 7.34027 238.269 7.02369 238.464 6.82843L241.293 4L238.464 1.17157C238.269 0.976311 238.269 0.659728 238.464 0.464466C238.66 0.269204 238.976 0.269204 239.172 0.464466L242.354 3.64645ZM1 30H0.5V28H1H1.5V30H1ZM25 4V3.5H242V4V4.5H25V4ZM1 28H0.5C0.5 14.469 11.469 3.5 25 3.5V4V4.5C12.0213 4.5 1.5 15.0213 1.5 28H1Z"
        fill={color || 'currentColor'}
      />
    </svg>
  )
}

const RightLine = ({ color }: { color: string }) => {
  return (
    <svg
      width="243"
      height="30"
      viewBox="0 0 243 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.646447 3.64645C0.451184 3.84171 0.451184 4.15829 0.646447 4.35355L3.82843 7.53553C4.02369 7.7308 4.34027 7.7308 4.53553 7.53553C4.7308 7.34027 4.7308 7.02369 4.53553 6.82843L1.70711 4L4.53553 1.17157C4.7308 0.976311 4.7308 0.659728 4.53553 0.464466C4.34027 0.269204 4.02369 0.269204 3.82843 0.464466L0.646447 3.64645ZM242 30H242.5V28H242H241.5V30H242ZM218 4V3.5H1V4V4.5H218V4ZM242 28H242.5C242.5 14.469 231.531 3.5 218 3.5V4V4.5C230.979 4.5 241.5 15.0213 241.5 28H242Z"
        fill={color || 'currentColor'}
      />
    </svg>
  )
}

const RiskScoreBoxContext = ({
  isRootNode,
  score,
  text,
}: {
  isRootNode?: boolean
  score: number
  text: string
}) => {
  return (
    <>
      <RiskScore score={score} />
      <div className={cn('text-xs', isRootNode && 'font-bold')}>{text}</div>
    </>
  )
}

export const RiskTree = ({
  risk_score,
  assessment_result,
  resources,
}: {
  risk_score: number
  assessment_result: AssessmentResult | null | undefined
  resources: Resources | null | undefined
}) => {
  return (
    <div className="w-full">
      <div className="flex justify-center h-[180px]">
        <div className="flex flex-col items-start justify-end relative">
          <LeftLine
            color={getSvgColor(assessment_result?.likelihood_score || 0)}
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="border rounded-3xl p-5 w-[150px] flex flex-col items-center gap-2 relative left-[-70px]">
                <RiskScoreBoxContext
                  score={assessment_result?.likelihood_score || 0}
                  text="Likelihood Score"
                />
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-[220px] bg-white text-slate-500 border">
              <p>{t('likelihoodScoreTooltip')}</p>
            </TooltipContent>
          </Tooltip>
        </div>
        <div>
          <div className="flex flex-col items-center gap-2 border rounded-3xl p-5">
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex flex-col justify-center items-center gap-2">
                  <RiskScoreBoxContext
                    score={risk_score}
                    text="Risk Score"
                    isRootNode
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                className="max-w-[220px] bg-white text-slate-500 border"
              >
                <p>{t('riskScoreTooltip')}</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
        <div className="flex flex-col items-end justify-end relative">
          <RightLine color={getSvgColor(resources?.impact_score || 0)} />
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="border rounded-3xl p-5 w-[150px] flex flex-col items-center gap-2 relative left-[70px]">
                <RiskScoreBoxContext
                  score={resources?.impact_score || 0}
                  text="Impact Score"
                />
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-[220px] bg-white text-slate-500 border">
              <p>{t('impactScoreTooltip')}</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
      <div className="flex"></div>
    </div>
  )
}
