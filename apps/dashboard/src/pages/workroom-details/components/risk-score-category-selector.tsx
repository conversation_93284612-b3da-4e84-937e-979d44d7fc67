/*eslint-disable max-lines*/
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  riskScoreCategoryToClass,
} from '@libs/ui'
import { t } from 'i18next'

import { Loader2 } from 'lucide-react'
import { CaseStatus } from 'prime-front-service-client/src/models/CaseStatus'
import { RiskScoreCategory } from 'prime-front-service-client/src/models/RiskScoreCategory'
import { cn } from '@libs/common'
import type { ExternalCase } from 'prime-front-service-client'
import { useUpdateRiskScoreCategory } from '../../../api/use-cases-api'
import { toast } from 'sonner'

interface RiskScoreCategorySelectorProps {
  caseInfo: ExternalCase
  onDone: () => void
}

export const RiskScoreCategorySelector = ({
  caseInfo,
  onDone,
}: RiskScoreCategorySelectorProps) => {
  const {
    mutate: updateRiskScoreCategoryMutation,
    isPending: isUpdatePending,
  } = useUpdateRiskScoreCategory()

  const { risk_score_category } = caseInfo.issue_analysis

  const reclassifyChange = async (value: RiskScoreCategory) => {
    if (value === RiskScoreCategory.None) {
      //   setOpen(true)
      onDone()
    } else {
      await handleUpdateRiskScoreCategory(value as RiskScoreCategory)
    }
  }

  const handleUpdateRiskScoreCategory = async (value: RiskScoreCategory) => {
    updateRiskScoreCategoryMutation(
      {
        source_id: caseInfo.source_id,
        issue_id: caseInfo.issue_id,
        risk_score_category: value as RiskScoreCategory,
      },
      {
        onSuccess: () => {
          toast.success('Risk score category updated successfully')
          //   refetch()
          onDone()
        },
        onError: () => {
          toast.error('Failed to update risk score category')
        },
      }
    )
  }
  return (
    <Select
      disabled={caseInfo.status === CaseStatus.done}
      onValueChange={async (value) => {
        await reclassifyChange(value as RiskScoreCategory)
      }}
      value={risk_score_category}
    >
      <SelectTrigger
        className={cn(
          'flex items-center h-10 w-28',
          risk_score_category === 'intervene'
            ? riskScoreCategoryToClass[RiskScoreCategory.intervene]
            : risk_score_category === 'analyze'
            ? riskScoreCategoryToClass[RiskScoreCategory.analyze]
            : riskScoreCategoryToClass[RiskScoreCategory.monitor],
          'h-10'
        )}
      >
        {isUpdatePending ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <div>
            {(risk_score_category as RiskScoreCategory) ||
              t('workroomPage.reclassify')}
          </div>
        )}
      </SelectTrigger>
      <SelectContent className="capitalize w-[180px]">
        <SelectItem value={RiskScoreCategory.intervene}>
          <div
            className={cn(
              'flex items-center justify-around h-8 w-24',
              riskScoreCategoryToClass[RiskScoreCategory.intervene]
            )}
          >
            {RiskScoreCategory.intervene}
          </div>
        </SelectItem>
        <SelectItem className="flex" value={RiskScoreCategory.analyze}>
          <div
            className={cn(
              'flex items-center justify-around h-8 w-24',
              riskScoreCategoryToClass[RiskScoreCategory.analyze]
            )}
          >
            {RiskScoreCategory.analyze}
          </div>
        </SelectItem>
        <SelectItem value={RiskScoreCategory.monitor}>
          <div
            className={cn(
              'flex items-center justify-around h-8 w-24',
              riskScoreCategoryToClass[RiskScoreCategory.monitor]
            )}
          >
            {RiskScoreCategory.monitor}
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  )
}
