import type {
  CaseComment,
  ExternalCase,
  ExternalCaseHistory,
} from 'prime-front-service-client'
import { CaseStatus } from 'prime-front-service-client'
import { useEffect, useMemo, useState } from 'react'
import {
  Badge,
  Button,
  JiraIcn,
  JiraIssueDetails,
  Separator,
  Summary5W,
  Tabs,
  TabsList,
  TabsTrigger,
} from '@libs/ui'
import {
  BlendIcon,
  CheckIcon,
  CircleCheck,
  Loader2,
  MessageSquareTextIcon,
} from 'lucide-react'
import { t } from 'i18next'
import { UserMentionInput } from '../../../components/user-mention-input'
import { LabelSelector } from './label-selector'
import { PrimeRiskModal } from './prime-risk-modal'
import { CaseStatusDropdown } from './case-status-dropdown'

interface CaseInfoProps {
  caseInfo: ExternalCase
  pendingComment: boolean
  successComment: boolean
  setIssueComment: (comment: string) => void
  refetchCase: () => void
  reopenCase: () => void
}

const auditActionMap: Record<
  string,
  {
    color: string
    icon: JSX.Element
    text: string | null
    extraText?: (args: any) => string | null
  }
> = {
  create_case: {
    color: 'bg-blue-50',
    icon: <MessageSquareTextIcon className="w-4 h-4 text-slate-500" />,
    text: null,
  },
  override_risk_category: {
    color: 'bg-yellow-50',
    icon: <BlendIcon className="w-4 h-4 text-violet-600" />,
    text: 'activityLog.riskCategoryWasOverridden',
    extraText: (actionArgs: {
      old_risk_score_category: string
      new_risk_score_category: string
    }) => {
      return actionArgs?.old_risk_score_category &&
        actionArgs.new_risk_score_category
        ? `from ${actionArgs.old_risk_score_category} to ${actionArgs.new_risk_score_category}`
        : ''
    },
  },
  update_status: {
    color: 'bg-green-50',
    icon: <CheckIcon className="w-4 h-4 text-emerald-600" />,
    text: 'activityLog.statusWasUpdated',
    extraText: (actionArgs: {
      old_status: CaseStatus
      new_status: CaseStatus
      reason?: string
    }) => {
      return actionArgs.old_status && actionArgs.new_status
        ? `from ${actionArgs.old_status} to ${actionArgs.new_status}. ${
            actionArgs.reason ? 'Reason: ' + actionArgs.reason : ''
          }`
        : ''
    },
  },
  user_view_case: {
    color: 'bg-green-50',
    icon: <CheckIcon className="w-4 h-4 text-emerald-600" />,
    text: 'activityLog.userViewCase',
  },
}

export const CaseInfo = ({
  caseInfo,
  pendingComment,
  successComment,
  setIssueComment,
  refetchCase,
  reopenCase,
}: CaseInfoProps) => {
  const [comment, setComment] = useState('')
  const [summary, setSummary] = useState(true)
  const [jiraFields, setJiraFields] = useState<{
    [key: string]: string
  }>(caseInfo?.provider_fields)

  const shortAiSummary = useMemo(() => {
    return caseInfo?.issue_analysis?.short_ai_summary
  }, [caseInfo])

  const activityLog = useMemo(() => {
    const mergedData = [
      ...(caseInfo?.history || []),
      ...(caseInfo?.comments || []),
    ]
    return mergedData.sort(
      (a, b) => b.created_at.getTime() - a.created_at.getTime()
    )
  }, [caseInfo])

  const riskScoreCategory = caseInfo.issue_analysis.risk_score_category

  useEffect(() => {
    setJiraFields(caseInfo.provider_fields)
  }, [caseInfo])

  useEffect(() => {
    if (successComment) {
      setComment('')
    }
  }, [successComment])

  return (
    <div className="px-4">
      <div className="flex items-center justify-between gap-2 top-0 mb-8 z-10">
        <h1 className="text-2xl">{caseInfo?.title}</h1>
      </div>
      {caseInfo?.status === CaseStatus.done && (
        <div className="mb-4 mt-8 p-6 bg-teal-50 text-teal-700 rounded border-l-4 border-teal-700 font-light hover:bg-teal-50/80">
          <h1 className="flex items-center gap-2 mb-3 text-lg font-semibold">
            <CircleCheck />
            <span>{t('workroomPage.riskReviewCompleted')}</span>
          </h1>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CaseStatusDropdown
            caseInfo={caseInfo}
            refetchCase={refetchCase}
            reopenCase={reopenCase}
          />

          <Badge
            size="lg"
            className="capitalize"
            variant={
              riskScoreCategory === 'None' ? 'analyze' : riskScoreCategory
            }
          >
            {caseInfo.issue_analysis.risk_score_category}
          </Badge>
        </div>

        <PrimeRiskModal
          assessment_result={
            caseInfo.issue_analysis.risk_factors.assessment_result
          }
          resources={caseInfo.issue_analysis.risk_factors.resources}
          risk_score={caseInfo.issue_analysis.risk_score || 0}
        />
      </div>

      <div className="my-5">
        <LabelSelector
          source_id={caseInfo.source_id}
          issue_id={caseInfo.issue_id}
          caseLabels={caseInfo.labels}
        />
      </div>

      <Separator className="my-4" />
      {shortAiSummary && (
        <div className="text-card-foreground mb-6 font-light">
          <div className="flex items-center justify-between">
            <h3 className="text-lg mb-3  font-bold capitalize">
              {t('expectedOutcome')}
            </h3>
            <Tabs
              value={summary ? 'summary' : 'detailed'}
              onValueChange={(value) => setSummary(value === 'summary')}
              className="w-fit"
            >
              <TabsList>
                <TabsTrigger value="summary" className="capitalize">
                  {t('summary')}
                </TabsTrigger>
                <TabsTrigger value="detailed" className="capitalize">
                  {t('detailed')}
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          {summary ? (
            <p
              dangerouslySetInnerHTML={{
                __html: shortAiSummary || '',
              }}
            ></p>
          ) : (
            <Summary5W summary={caseInfo.issue_analysis.long_ai_summary_5w} />
          )}
        </div>
      )}

      <div className="flex mt-8 mb-3 items-center gap-2">
        <JiraIcn className="h-4 w-4" />
        <h3 className="font-medium capitalize">{t('importedFields')}</h3>
      </div>
      <JiraIssueDetails
        issueNumber={caseInfo?.issue_id}
        issueLink={caseInfo?.link}
        customDetails={jiraFields}
        providerFieldsTypes={
          // TODO: discuss with backend team to add provider_fields_min_schema to issue_analysis
          caseInfo?.provider_fields_min_schema || null
        }
        issueLinks={caseInfo?.issue_analysis.issue_links || []}
      />

      <div className="text-sm font-semibold capitalize mb-4">
        {t('workroomPage.activityLog')}
      </div>

      <div className="activity-log border-l space-y-5 ml-3">
        <div className="pl-8 relative flex items-center">
          <div className="absolute left-[-16px] top-0 border rounded-full bg-white w-8 h-8 flex justify-center items-center">
            <MessageSquareTextIcon className="w-4 h-4 text-slate-500" />
          </div>
          <UserMentionInput value={comment} onChange={setComment} />
          <Button
            className="absolute right-1 h-7 shadow-none capitalize"
            size="sm"
            onClick={() => setIssueComment(comment)}
            disabled={pendingComment}
            dataTestId="post-comment-button"
          >
            {pendingComment && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {t('workroomPage.post')}
          </Button>
        </div>

        {activityLog.map((item: ExternalCaseHistory | CaseComment) => (
          <div
            key={crypto.randomUUID()}
            className="pl-8 flex items-center relative"
          >
            <div className="absolute left-[-16px] border rounded-full bg-white w-8 h-8 flex justify-center items-center">
              {'audit_action' in item
                ? auditActionMap?.[item.audit_action]?.icon
                : auditActionMap?.['create_case']?.icon}
            </div>
            <div className="flex items-center justify-between w-full">
              <div>
                <span className="text-xs text-slate-500">{item.user}</span>
                <div className="text-sm">
                  {'audit_action' in item && item?.audit_action
                    ? t(auditActionMap?.[item.audit_action]?.text || '')
                    : t(('text' in item && item.text) || '')}
                  <span className="ml-1">
                    {'audit_action' in item &&
                      item.audit_action &&
                      (() => {
                        const extraText =
                          auditActionMap?.[item.audit_action]?.extraText
                        return typeof extraText === 'function'
                          ? extraText(item.audit_action_args)
                          : null
                      })()}
                  </span>
                </div>
              </div>
              <span className="text-xs text-slate-500">
                {item.created_at?.toLocaleDateString()}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
