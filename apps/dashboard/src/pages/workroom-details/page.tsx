import React, { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router'
import type { ImplementationStatusUpdate } from 'prime-front-service-client'
import { CaseStatus } from 'prime-front-service-client'
import { cn, MetaTagsManager } from '@libs/common'
import { toast } from 'sonner'
import {
  useAddCommentToCase,
  useGetCase,
  useUpdateCaseStatus,
  useUpdateRecommendation,
} from '../../api/use-cases-api'
import { t } from 'i18next'
import { AlertNotFound, Button, ClipLoader, CollapseIcn } from '@libs/ui'
import { CaseInfo } from './components/case-info'
import { ConcernsSection } from './components/concerns-section'
import { ArrowLeft } from 'lucide-react'

export const WorkroomDetailsPage = () => {
  const params = useParams()
  const navigate = useNavigate()
  const { sourceId, issueId } = params

  const { data, isPending, isError, isSuccess, refetch, isRefetching } =
    useGetCase(Number(sourceId), issueId || '')

  const { mutate: updateRecommendationsMutation } = useUpdateRecommendation()
  const updateCommentsMutation = useAddCommentToCase()
  const updateCaseStatusMutation = useUpdateCaseStatus()

  const [status, setStatus] = useState<CaseStatus | undefined>(data?.status)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [summary] = useState(true)

  useEffect(() => {
    setStatus(data?.status)
  }, [data?.status])

  const setIssueComment = async (comment: string) => {
    updateCommentsMutation.mutate(
      {
        source_id: sourceId as unknown as number,
        issue_id: issueId || '',
        comment,
      },
      {
        onSuccess: async () => {
          await refetch()
          toast.success(t('commentUpdatedSuccessfully'))
        },
        onError: async () => {
          toast.error(t('errors.commentUpdateFailed'))
        },
      }
    )
  }

  const updateCaseStatus = async (status: CaseStatus) => {
    updateCaseStatusMutation.mutate(
      {
        source_id: sourceId as unknown as number,
        issue_id: issueId || '',
        status,
      },
      {
        onSuccess: async () => {
          await refetch()
          status === CaseStatus.done
            ? toast.success(t('caseCompletedSuccessfully'))
            : toast.success(t('caseReopenedSuccessfully'))
        },
        onError: async () => {
          status === CaseStatus.done
            ? toast.error(t('errors.completingCaseFailed'))
            : toast.error(t('errors.reopeningCaseFailed'))
        },
      }
    )
  }

  const reopenCase = async () => {
    await updateCaseStatus(CaseStatus.open)
  }

  const onConfirmedRecommendationsChange = async (
    recs: ImplementationStatusUpdate[],
    customUpdate?: boolean
  ) => {
    updateRecommendationsMutation(
      {
        source_id: sourceId as unknown as number,
        issue_id: issueId || '',
        recommendationStatusUpdate: recs,
      },
      {
        onSuccess: async () => {
          if (customUpdate) {
            await refetch()
          }
        },
        onError: async () => {
          toast.error(t('errors.updatingRecommendationsFailed'))
        },
      }
    )
  }

  if (isPending) {
    return (
      <div
        data-testid="loading-spinner"
        className="flex justify-center items-center flex-col min-h-80"
      >
        <ClipLoader />
      </div>
    )
  }

  if (isError && !isRefetching) {
    return (
      <div className="flex justify-center items-center flex-col min-h-96">
        <div>
          <AlertNotFound
            title="Not found"
            description="The issue you are looking for does not exist"
          />
        </div>
      </div>
    )
  }

  return (
    <div className="flex-col bg-white grow h-screen overflow-hidden">
      <MetaTagsManager
        title={data?.title || 'Workroom'}
        description={
          data?.issue_analysis?.short_ai_summary || 'Workroom details'
        }
        siteName="Prime"
      />
      <div className="h-full">
        <div
          className={cn(
            'grid h-full transition-all duration-300',
            isCollapsed ? 'grid-cols-1' : 'grid-cols-[1fr_1.5fr]'
          )}
        >
          <div
            className={cn(
              'border h-full overflow-y-auto p-8 pb-8 transition-all duration-300',
              isCollapsed ? 'w-0 hidden' : 'w-full block'
            )}
          >
            <div className="flex items-center justify-start">
              <Button
                variant="ghost"
                dataTestId="back-button"
                className={cn('ml-2 mb-6 flex items-center gap-2 capitalize')}
                onClick={() => navigate(-1)}
              >
                <ArrowLeft size={16} />
                {t('back')}
              </Button>
            </div>
            {data && (
              <CaseInfo
                caseInfo={data}
                pendingComment={updateCommentsMutation.isPending}
                successComment={updateCommentsMutation.isSuccess}
                setIssueComment={setIssueComment}
                refetchCase={refetch}
                reopenCase={reopenCase}
              />
            )}
          </div>
          <div
            className={cn(
              'h-full overflow-y-auto transition-all duration-300',
              isCollapsed ? 'col-span-1' : ''
            )}
          >
            <div className="pt-4 px-6 flex items-center justify-between">
              <Button
                variant="ghost"
                className="p-2"
                dataTestId="collapse-button"
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                <CollapseIcn />
              </Button>
            </div>
            <div className="mt-8 grid h-full grid-flow-row grid-rows-[auto]">
              {isSuccess ? (
                <ConcernsSection
                  refetch={refetch}
                  caseDone={
                    status === CaseStatus.done ||
                    status === CaseStatus.dismissed ||
                    false
                  }
                  frameworkConcerns={data.framework_concerns}
                  primeConcerns={data.prime_concerns}
                  updateCaseStatus={updateCaseStatus}
                  onUpdateRecommendations={onConfirmedRecommendationsChange}
                  summary={summary}
                />
              ) : (
                <div>{t('somethingWentWrong')}</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
